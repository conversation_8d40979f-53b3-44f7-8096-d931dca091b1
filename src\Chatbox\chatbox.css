.disrupt-chat-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  font-family: 'Arial', sans-serif;
}

.help-button-container {
  position: relative;
  margin-bottom: 0px;
  display: flex;
  justify-content: flex-end;
}

.help-button {
  width: 100px;
  height: 100px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: transparent;
  border: none;
  padding: 0;
  /* box-shadow: 0 2px 5px rgba(0,0,0,0.2); */
}
.headerAvatar {
  width: 70px;
  height: 70px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: transparent;
  border: none;
  padding: 0;
  /* box-shadow: 0 2px 5px rgba(0,0,0,0.2); */
}

.help-button img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.headerAvatar img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.help-button:hover {
  transform: scale(1.05);
}

.hover-text {
  position: absolute;
  top: -30px;
  right: 80px;
  background-color: #fff;
  padding: 5px 10px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
  white-space: nowrap;
  font-size: 14px;
  color: #333;
}

.chat-box {
  width: 0;
  height: 0;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.chat-box.open {
  width: 380px;
  height: 700px;
}

.chat-header {
  background-color: #3470A1;
  padding: 5px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-header span {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  color: #fff;
}

.chat-box.open .chatboxbody{
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%; 
}


@media (max-width: 768px) {
  /* .chat-box-container{
    min-width: 95vw;
    background: #333;
  } */
  .chat-box.open {
    width: 90vw;
    height: 90vh;
    border-radius: 0;
  }
 
  /* .chat-header {
    flex-direction: column;
    align-items: flex-start;
  }
  .chat-header span {
    margin-left: 10px;
  } */
}




