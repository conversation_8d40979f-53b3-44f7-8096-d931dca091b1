import React, { useState } from 'react';
import Chart from 'react-apexcharts';

export default function CustomAreaChart() {
  const [chartData, setChartData] = useState({
    options: {
      chart: {
        type: 'area',
        height: 350,
        toolbar: { show: false },
        zoom: { enabled: false },
        animations: { enabled: false }
      },
      stroke: {
        curve: 'smooth',
        width: 3
      },
      colors: ['#5c6ac4', '#e23e57'],  // blue-ish and red-ish colors matching your image
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'light',
          type: 'vertical',
          shadeIntensity: 0.8,
          inverseColors: false,
          opacityFrom: 0.7,
          opacityTo: 0.4,
          stops: [0, 100]
        }
      },
      markers: {
        size: 5,
        strokeWidth: 0,
        hover: { size: 7 }
      },
      xaxis: {
        categories: ['00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00'],
        labels: {
          style: { fontSize: '12px', colors: '#8892a0' }
        },
        axisBorder: { show: false },
        axisTicks: { show: false }
      },
      yaxis: {
        labels: {
          style: { fontSize: '12px', colors: '#8892a0' }
        },
        min: 0,
        max: 120,
        tickAmount: 6,
        forceNiceScale: true
      },
      legend: {
        show: true,
        position: 'bottom',
        horizontalAlign: 'center',
        fontSize: '14px',
        markers: { radius: 12 },
        labels: { colors: ['#5c6ac4', '#e23e57'] }
      },
      tooltip: {
        shared: true,
        intersect: false,
        y: {
          formatter: val => val.toFixed(0)
        }
      },
      dataLabels: { enabled: false }
    },
    series: [
      {
        name: 'series1',
        type: 'area',
        data: [30, 35, 25, 50, 40, 110, 100]
      },
      {
        name: 'series2',
        type: 'area',
        data: [10, 30, 45, 35, 35, 50, 40]
      }
    ]
  });

  return <Chart options={chartData.options} series={chartData.series} type="area" height={350} />;
}
