import React, { useEffect, useRef, useState, useCallback } from 'react';

export default function MicConnectionWithWebSocket({
  apiUrl = 'https://beunileverchatbot.disruptlabs.tech',
  sessionId,
  getOrCreateSessionId,
  onPartialTranscript,
  onFinalTranscript,
  disabled = false,
  autoSubmitFinal = false,
  showTranscriptUI = true,
}) {
  const [isRecording, setIsRecording] = useState(false);
  const [finalTranscript, setFinalTranscript] = useState('');
  const [interimTranscript, setInterimTranscript] = useState('');
  const [audioLevel, setAudioLevel] = useState(0);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [transcriptVisible, setTranscriptVisible] = useState(false);

  // Refs
  const wsRef = useRef(null);
  const mediaStreamRef = useRef(null);
  const audioContextRef = useRef(null);
  const captureBufferRef = useRef(new Float32Array(0));
  const hasSentFinalRef = useRef(false);
  const finalSentRef = useRef(false);
  const sendLockedRef = useRef(false);
  const recordedChunksRef = useRef([]);

  const SAMPLE_RATE = 16000;
  const CHANNELS = 1;
  const BUFFER_SIZE = 1024;
  const CHUNK_DURATION_MS = 200;

  const floatTo16BitPCM = useCallback((float32Array) => {
    const len = float32Array.length;
    const buffer = new ArrayBuffer(len * 2);
    const view = new DataView(buffer);
    let offset = 0;
    for (let i = 0; i < len; i++, offset += 2) {
      let s = Math.max(-1, Math.min(1, float32Array[i]));
      view.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7fff, true);
    }
    return buffer;
  }, []);

  const downsampleBuffer = useCallback((buffer, srcRate, targetRate) => {
    if (targetRate === srcRate) return buffer;
    const ratio = srcRate / targetRate;
    const newLength = Math.round(buffer.length / ratio);
    const result = new Float32Array(newLength);
    let offsetResult = 0;
    while (offsetResult < newLength) {
      const nextOffsetBuffer = offsetResult * ratio;
      const lower = Math.floor(nextOffsetBuffer);
      const upper = Math.min(Math.ceil(nextOffsetBuffer), buffer.length - 1);
      const weight = nextOffsetBuffer - lower;
      result[offsetResult] = buffer[lower] * (1 - weight) + buffer[upper] * weight;
      offsetResult++;
    }
    return result;
  }, []);

  const clearTranscript = useCallback(() => {
    setFinalTranscript('');
    setInterimTranscript('');
    hasSentFinalRef.current = false;
    finalSentRef.current = false;
    setTranscriptVisible(false);
  }, []);

  const updateTranscriptDisplay = useCallback(() => {
    if (finalTranscript || interimTranscript) {
      setTranscriptVisible(true);
    }
  }, [finalTranscript, interimTranscript]);

  const startAudioStreaming = useCallback(async () => {
    try {
      setConnectionStatus('connecting');
      clearTranscript();
      finalSentRef.current = false;
      hasSentFinalRef.current = false;
      sendLockedRef.current = false;
      recordedChunksRef.current = [];

      // Check microphone permissions
      if (navigator.permissions) {
        const permissionStatus = await navigator.permissions.query({ name: 'microphone' });
        if (permissionStatus.state === 'denied') {
          throw new Error('Microphone access denied. Please enable in browser settings.');
        }
      }

      // Acquire microphone
      mediaStreamRef.current = await navigator.mediaDevices.getUserMedia({
        audio: {
          channelCount: CHANNELS,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: SAMPLE_RATE
        },
        video: false
      });

      // Create audio context
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)({
        sampleRate: SAMPLE_RATE
      });

      console.log('Actual input sampleRate:', audioContextRef.current.sampleRate);

      // Create WebSocket connection
      const wsUrl = `${apiUrl.replace(/^https?/, 'wss')}/stt/transcribe`;
      wsRef.current = new WebSocket(wsUrl);
      wsRef.current.binaryType = 'arraybuffer';

      wsRef.current.onopen = () => {
        console.log('WebSocket open');
        setIsRecording(true);
        setConnectionStatus('connected');
        captureBufferRef.current = new Float32Array(0);
        setTranscriptVisible(true);
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          if (data.text) {
            if (data.type === 'final') {
              setFinalTranscript((prev) => prev + data.text + ' ');
              if (onFinalTranscript) onFinalTranscript(data.text);
              if (autoSubmitFinal && !hasSentFinalRef.current && data.text.trim()) {
                finalSentRef.current = true;
                hasSentFinalRef.current = true;
                setTimeout(() => {
                  if (onFinalTranscript) {
                    onFinalTranscript(finalTranscript.trim());
                  }
                }, 0);
              }
            } else if (data.type === 'partial') {
              setInterimTranscript(data.text);
              if (onPartialTranscript) onPartialTranscript(data.text);
              if (autoSubmitFinal && data.is_paused && !hasSentFinalRef.current && finalTranscript.trim()) {
                finalSentRef.current = true;
                hasSentFinalRef.current = true;
                setTimeout(() => {
                  if (onFinalTranscript) onFinalTranscript(finalTranscript.trim());
                }, 0);
              }
            }
          }
        } catch (err) {
          console.warn('Failed to parse STT message', err, event.data);
        }
      };

      wsRef.current.onerror = (err) => {
        console.error('WebSocket error:', err);
        setConnectionStatus('disconnected');
        stopAudioStreaming();
      };

      wsRef.current.onclose = () => {
        console.log('WebSocket closed');
        setConnectionStatus('disconnected');
        if (!finalSentRef.current && finalTranscript.trim() && !hasSentFinalRef.current) {
          finalSentRef.current = true;
          hasSentFinalRef.current = true;
          if (onFinalTranscript) onFinalTranscript(finalTranscript.trim());
        }
        stopAudioStreaming();
      };

      // Define AudioWorkletProcessor as a Blob
      const processorScript = `
        class AudioProcessor extends AudioWorkletProcessor {
          process(inputs, outputs, parameters) {
            const input = inputs[0];
            const output = outputs[0];
            for (let channel = 0; channel < input.length; channel++) {
              output[channel].set(input[channel]);
            }
            return true;
          }
        }
        registerProcessor('audio-processor', AudioProcessor);
      `;
      const blob = new Blob([processorScript], { type: 'application/javascript' });
      const url = URL.createObjectURL(blob);

      // Load the AudioWorklet module from the Blob URL
      await audioContextRef.current.audioWorklet.addModule(url);
      const workletNode = new AudioWorkletNode(audioContextRef.current, 'audio-processor');

      const source = audioContextRef.current.createMediaStreamSource(mediaStreamRef.current);
      source.connect(workletNode);
      workletNode.connect(audioContextRef.current.destination);

      // Process the audio stream
      workletNode.port.onmessage = (e) => {
        const { buffer } = e.data;
        if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN && !sendLockedRef.current) {
          try {
            wsRef.current.send(buffer);
          } catch (e) {
            console.warn('Failed to send audio chunk', e);
          }
        }
      };

      console.log('Microphone started successfully');
    } catch (error) {
      console.error('Error starting microphone:', error);
      setConnectionStatus('disconnected');
      stopAudioStreaming();
      if (onPartialTranscript) onPartialTranscript('Error: Could not access microphone. Please check permissions.');
    }
  }, [apiUrl, finalTranscript, autoSubmitFinal, onFinalTranscript, onPartialTranscript]);

  const stopAudioStreaming = useCallback(() => {
    setIsRecording(false);
    sendLockedRef.current = true;
    setAudioLevel(0);

    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      try {
        wsRef.current.close();
      } catch (e) {
        console.warn('Error closing ws', e);
      }
    }

    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(track => track.stop());
      mediaStreamRef.current = null;
    }

    if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
      try {
        audioContextRef.current.close();
      } catch (e) {
        console.warn('Error closing audio context', e);
      }
      audioContextRef.current = null;
    }

    console.log('Microphone stopped');
  }, []);

  const handleMicClick = async () => {
    if (disabled) return;
    
    if (isRecording) {
      stopAudioStreaming();
    } else {
      await startAudioStreaming();
    }
  };

  const handleUseTranscript = () => {
    const text = finalTranscript.trim();
    if (!text) return;
    
    if (onFinalTranscript) {
      onFinalTranscript(text);
    }
    clearTranscript();
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopAudioStreaming();
    };
  }, [stopAudioStreaming]);

  // Update transcript display visibility
  useEffect(() => {
    updateTranscriptDisplay();
  }, [updateTranscriptDisplay]);

  const micButtonStyle = {
    background: isRecording ? '#f44336' : '#2196f3',
    color: 'white',
    border: 'none',
    borderRadius: '50%',
    width: '40px',
    height: '40px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: disabled ? 'not-allowed' : 'pointer',
    fontSize: '16px',
    transition: 'all 0.3s ease',
    position: 'relative',
    ...(isRecording && {
      animation: 'pulse 1.5s infinite',
      boxShadow: '0 0 0 0 rgba(244, 67, 54, 0.7)'
    })
  };

  const transcriptContainerStyle = {
    position: 'relative',
    marginBottom: '10px',
    background: '#fff',
    border: '1px solid #e0e0e0',
    borderRadius: '8px',
    padding: '12px',
    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
    display: transcriptVisible ? 'block' : 'none',
  };

  const transcriptStyle = {
    minHeight: '40px',
    maxHeight: '120px',
    overflowY: 'auto',
    marginBottom: '8px',
    fontSize: '14px',
    lineHeight: '1.4',
  };

  const audioVisualizerStyle = {
    height: '4px',
    background: 'rgba(0, 0, 0, 0.1)',
    borderRadius: '2px',
    overflow: 'hidden',
    marginTop: '8px',
  };

  const audioLevelStyle = {
    height: '100%',
    width: `${audioLevel}%`,
    background: 'linear-gradient(90deg, #4caf50, #2196f3)',
    transition: 'width 0.1s',
  };

  const connectionStatusStyle = {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    fontSize: '12px',
    color: connectionStatus === 'connected' ? '#4caf50' : 
           connectionStatus === 'connecting' ? '#ff9800' : '#f44336',
    marginBottom: '8px',
  };

  return (
    <div style={{ position: 'relative' }}>
      {/* Connection Status */}
      <div style={connectionStatusStyle}>
        <div
          style={{
            width: '8px',
            height: '8px',
            borderRadius: '50%',
            backgroundColor: 'currentColor',
          }}
        />
        <span>
          {connectionStatus === 'connected' ? 'Connected' :
           connectionStatus === 'connecting' ? 'Connecting...' : 'Disconnected'}
        </span>
      </div>

      {/* Transcript Container */}
      {showTranscriptUI && (
        <div style={transcriptContainerStyle}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
            <h4 style={{ margin: 0, fontSize: '14px', fontWeight: '500' }}>
              {isRecording ? 'Listening...' : 'Voice Transcription'}
            </h4>
            <button
              onClick={clearTranscript}
              style={{
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                color: '#666',
                fontSize: '16px',
              }}
            >
              ×
            </button>
          </div>
          
          <div style={transcriptStyle}>
            {finalTranscript}
            <span style={{ opacity: 0.7, fontStyle: 'italic' }}>
              {interimTranscript}
            </span>
            {!finalTranscript && !interimTranscript && isRecording && (
              <span style={{ color: '#999' }}>Listening for speech...</span>
            )}
          </div>
          
          {/* Audio Visualizer */}
          {isRecording && (
            <div style={audioVisualizerStyle}>
              <div style={audioLevelStyle} />
            </div>
          )}
          
          {/* Use Transcript Button */}
          {finalTranscript.trim() && !autoSubmitFinal && (
            <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <button
                onClick={handleUseTranscript}
                style={{
                  background: '#2196f3',
                  color: 'white',
                  border: 'none',
                  borderRadius: '16px',
                  padding: '6px 12px',
                  fontSize: '12px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px',
                }}
              >
                📤 Send
              </button>
            </div>
          )}
        </div>
      )}

      {/* Mic Button */}
      <button 
        onClick={handleMicClick} 
        disabled={disabled}
        style={micButtonStyle}
        title={isRecording ? 'Stop Recording' : 'Start Recording'}
      >
        {isRecording ? '⏹️' : '🎤'}
      </button>

      {/* Pulse animation styles */}
      <style jsx>{`
        @keyframes pulse {
          0% { box-shadow: 0 0 0 0 rgba(244, 67, 54, 0.7); }
          70% { box-shadow: 0 0 0 10px rgba(244, 67, 54, 0); }
          100% { box-shadow: 0 0 0 0 rgba(244, 67, 54, 0); }
        }
        
        button:disabled {
          opacity: 0.6;
          cursor: not-allowed !important;
        }
      `}</style>
    </div>
  );
}