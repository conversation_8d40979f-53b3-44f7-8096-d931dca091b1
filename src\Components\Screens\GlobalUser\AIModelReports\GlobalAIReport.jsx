import React, { useEffect, useState } from 'react';
import AIModelAndReportsScreen from './model_and_reports';
import { Card, CardBody, Col, Container, Row } from 'reactstrap';
import AreaService from '../../../../api/areaService';
import MyComponentLoader from '../../../../CommonElements/Spinner/loader3';
import { IoWarning } from 'react-icons/io5';
import './custom.css'
import DateFilter from './Components/Dates/DateFilter';
import { getCurrentWeekWithYear } from '../../../../utils/currentWeekWithYear';
import FilterComponent from '../../../FilterComponent/Filters';

export default function GlobalAIReport() {
  const [acceptedRecords, setAcceptedRecords] = useState([]);
  const [SelectedFilters, setSelectedFilters] = useState({
    identifier: '',
    period: "",
  });
  const [loading, setloading] = useState(true)
  const getAiverifiedAlerts = () => {
    setloading(true)
    const identifier = SelectedFilters.identifier;
    const period = SelectedFilters.period;
    const payload = {
      "factory_id": JSON.parse(localStorage.getItem('userData'))?.factory.id || 0,
      "factory_name": JSON.parse(localStorage.getItem('userData'))?.factory.name || '',
      [identifier]: period, 
    }
    AreaService.get_months_records(payload).then((res) => {
      setAcceptedRecords(res.data);
    }).catch((err) => {
      console.log(err);
    }).finally(() => {
      setloading(false)
    })
  }
  useEffect(() => {
    if (SelectedFilters.identifier && SelectedFilters.period) {
      getAiverifiedAlerts();
    }
  }, [SelectedFilters]);



  return (
    <>
      <Container fluid={true} className="dashboard-first-page">
     
        <Row>
          <Col md='6'>
          <h4 style={{ fontSize: '20px' }}>Verified Operational AI Violations</h4>
          </Col>
          <Col md='6' className='d-flex justify-content-end'>
          <FilterComponent SelectedFilters={SelectedFilters} setSelectedFilters={setSelectedFilters} /> 
          </Col>
        </Row>
 
        <Row className='mt-2'> 
          {acceptedRecords?.map((item, key) => (
            <Col xxl='3' xl="4" sm="6" >
              <Card  key={key}>
                <CardBody className="p-3 d-flex gap-2 flex-column justify-content-center align-items-center" style={{ minHeight: "170px" }}>
                  {loading ? <MyComponentLoader /> : <>
                    <p className="pb-0 mb-0" style={{ fontSize: 18, color: "rgb(56, 56, 56)", fontWeight: 400 }}>{item.week || item.month || "N/A"}</p>
                    <h2 className='d-flex align-items-center'>
                      <span className='me-2'><IoWarning style={{ width: '40px', height: '40px', borderRadius: '50%', padding: '8px', background: '#1e67d6', color: 'white' }} /></span>
                      <p style={{ fontSize: 30, fontWeight: 500, color: "rgb(89, 89, 89)" }}>{item.accepted_records==0 ? '0' : item.accepted_records || "N/A"}</p>
                    </h2>
                  </>}
                </CardBody>
              </Card>
            </Col>
          ))}
        </Row>



      </Container>

      <AIModelAndReportsScreen area={false} />
      {console.clear()}
    </>
  );
}