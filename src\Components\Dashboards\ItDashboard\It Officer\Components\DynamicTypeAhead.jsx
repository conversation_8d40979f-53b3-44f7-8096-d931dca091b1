import React from "react";
import { FormGroup, Label } from "reactstrap";
import { Typeahead } from "react-bootstrap-typeahead";
import 'react-bootstrap-typeahead/css/Typeahead.css';

export const DynamicTypeaheadField = ({
  label,
  id,
  options = [],
  selected = [],
  onChange,
  placeholder = "",
  multiple = false,
  disabled = false,
  labelKey="name",
  miniAlert=false,
  miniAlertMsg="",
  error=""
}) => { 
  return (
    <FormGroup>
      <Label for={id}>{label}</Label>
      <Typeahead
        id={id}
        labelKey={labelKey}
        multiple={multiple}
        options={options}
        placeholder={placeholder}
        selected={selected}
        onChange={onChange}
        className={error ? "is-invalid" : ""}
      />
      {error && <div className="invalid-feedback" style={{display: 'block'}}>{error}</div>}
      {miniAlert && <p className="text-danger m-0 text-center" style={{fontSize:"12px"}}>{miniAlertMsg}</p>}
    </FormGroup>
  );
};
