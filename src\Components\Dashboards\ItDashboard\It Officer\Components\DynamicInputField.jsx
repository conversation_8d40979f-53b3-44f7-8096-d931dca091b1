import { FormGroup, Input, Label } from "reactstrap";

export const DynamicInputField = ({ label, type, name, value, onChange, isDropdown = false, options = [], showPassMsg, error }) => {
 
  return (
    <FormGroup>
      <Label for={name}>{label}</Label>
      {isDropdown ? (
        <Input type="select" name={name} id={name} value={value} onChange={onChange} className={error ? "is-invalid" : ""}>
          {/* Options render kar rahe hain */}
          <option value="">Select role</option>
          {options.map((option, index) => (
            <option key={index} value={option.value}>
              {option.name}
            </option>
          ))}
        </Input>
      ) : (
        <Input type={type} name={name} id={name} value={value} onChange={onChange} className={error ? "is-invalid" : ""} />
      )}
      {error && <div className="invalid-feedback">{error}</div>}
      {/* {showPassMsg && <p className="text-muted mt-2 small text-center">Password must be at least 8 characters long including one uppercase letter, one lowercase letter, one number, and one special character. </p>} */}
    </FormGroup>
  );
};
