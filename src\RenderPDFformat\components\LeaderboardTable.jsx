import React from "react";
import SimpleTable from "./TableForPDF";

const LeaderboardTable = ({ leaderboardData = [] }) => {
  const columns = [
    { header: "#", key: (row, idx) => idx + 1, width: "50px", align: "left" },
    { header: "Name", key: "Name", align: "left" },
    { header: "Area ID", key: "areaid", align: "center" },
    { header: "Alerts", key: "totalalert", align: "center" },
    { header: "Compliance %", key: "compliance_percentage", align: "center" },
    { header: "Time Spent (mins)", key: "time", align: "center" },
  ];

  // Format data on the fly for keys that are functions or need % / units
  const formatData = leaderboardData.map((row) => ({
    ...row,
    compliance_percentage: row.compliance_percentage + "%",
    time: row.time + " mins",
  }));

  return <SimpleTable title="Leaderboard Performance" columns={columns} data={formatData} />;
};

export default LeaderboardTable;
