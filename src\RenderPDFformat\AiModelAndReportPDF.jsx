import React, { useEffect, useState } from "react";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import HeaderSection from "./components/HeaderSection";
import SummaryOverview from "./components/SummaryOverview";
import AccuracySection from "./components/AccuracySection";
import ModuleStatisticsTable from "./components/ModuleStatisticsTable";
import LeaderboardTable from "./components/LeaderboardTable";
import { transformReportData } from "./sampleData";
import { Container } from "reactstrap";
import { errorToast, successToast } from "../_helper/helper";
import { fetchRecordsWithFilter, fetchLeaderboardData, fetchHeatmapDataForReports, getSubareasDetailsForPDF, setDataFormat, executive_summary_for_report, get_alerts_for_report, fetchAnalyticsBars } from "./utils/reportApiUtils";
import AIModal from "../Components/Screens/GlobalUser/AIModelReports/Components/Modal/AIModal";
import { Send_PDF_through_Email } from "./utils/reportApiUtils"
import TestComponent from "./TestComponent";
import {validateFilters} from '../utils/formatDate'

const AIModelReportComponent = ({ data, verifiedAlerts = [] }) => {
  const transformedData = transformReportData(data);
  return (
    <div
      style={{
        padding: "40px",
        fontFamily: "Arial, sans-serif",
        width: "100%",
        minWidth: "1000px",
        maxWidth: "1000px",
        margin: "0 auto",
      }}
    >
      <Container fluid className="my-2">
        <HeaderSection filters={data.filters} />
        <SummaryOverview
          modules={transformedData.modules}
          summaryOfAlerts={data.summaryofAlerts}
        />
        <AccuracySection
          modules={transformedData.modules || []}
          heading="Accuracy of Al Models"
        />
        <AccuracySection
          modules={verifiedAlerts || []}
          flag={true}
          heading="Ai Verification Alerts"
        />
        <ModuleStatisticsTable modules={transformedData.modules} />
        {/* Leaderboard removed from here */}
      </Container>
    </div>
  );
};

const LeaderboardOnly = ({ leaderboardData }) => (
  <div
    style={{
      padding: "40px",
      fontFamily: "Arial, sans-serif",
      width: "1000px",
      margin: "0 auto",
      background: "white",
    }}
  >
    <LeaderboardTable leaderboardData={leaderboardData} />
  </div>
);

const DownloadAIModelReport = ({ data }) => {
  const [loading, setloading] = useState(false);
  const [verifiedAlerts, setVerifiedAlerts] = useState([]);
  const [leaderboardData, setLeaderboardData] = useState([]);
  const [PDF_file, setPDF_file] = useState()
  const [modal, setmodal] = useState(false)
  const toggleModal = () => setmodal(!modal)
  const [email_loader, setemail_loader] = useState(false)
  const [Email_modal_msg, setEmail_modal_msg] = useState("Generating PDF file...")
  const [receivers, setReceivers] = useState([]);
  const [ccEmails, setCcEmails] = useState([]);
  const [ShowSuccessMsg, setShowSuccessMsg] = useState(false)
  const [heatmapData, setHeatmapData] = useState({});
  const [subAreas, setSubAreas] = useState([])
  const [executiveSummary, setExecutiveSummary] = useState(undefined)
  const [progressData, setProgressData] = useState(undefined)
  const [alertsForReports, setAlertsForReports] = useState(undefined)
  console.log('heatmapData123', heatmapData)

  const downloadPDFf = async ({ send_through_email }) => {
    const {filters} =data
    const checkingFilters= validateFilters(filters,true)
    if(checkingFilters){
      if (send_through_email) {
        toggleModal();
        setemail_loader(true);
      }
      else {
        setloading(true);
      }
      try {
        // // Capture first page (everything except Leaderboard)
        // const firstPageElement = document.getElementById("report-content");
        // const firstCanvas = await html2canvas(firstPageElement, { scale: 2 });
        // const firstImgData = firstCanvas.toDataURL("image/png");
  
        // // Capture Leaderboard separately
        // const leaderboardElement = document.getElementById("report-leaderboard");
        // const leaderboardCanvas = await html2canvas(leaderboardElement, { scale: 2 });
        // const leaderboardImgData = leaderboardCanvas.toDataURL("image/png");
  
        // const pdf = new jsPDF("p", "mm", "a4");
        // const pdfWidth = pdf.internal.pageSize.getWidth();
        // const pdfHeight = pdf.internal.pageSize.getHeight();
  
        // // Calculate dimensions for first page image
        // const firstImgHeight = (pdfWidth * firstCanvas.height) / firstCanvas.width;
        // pdf.addImage(firstImgData, "PNG", 0, 0, pdfWidth, firstImgHeight);
  
        // // Add new page for leaderboard
        // pdf.addPage();
  
        // // Calculate dimensions for leaderboard image
        // const leaderboardImgHeight =
        //   (pdfWidth * leaderboardCanvas.height) / leaderboardCanvas.width;
  
        // pdf.addImage(leaderboardImgData, "PNG", 0, 0, pdfWidth, leaderboardImgHeight);
        // if (send_through_email) {
        //   const pdfBlob = pdf.output("blob");
        //   setPDF_file(pdfBlob);
        //   // successToast("PDF Generated for email")
        // } else {
        //   pdf.save("AI-Model-Report.pdf");
        // }
        const firstPageElement = document.getElementById("report-content");
         const el = document.getElementById("report-content"); // yahi container jisme <a> btn hai
  
        // Use html2canvas to capture full height
        const firstCanvas = await html2canvas(firstPageElement, { scale: 2 });
  
        // Convert to image
        const firstImgData = firstCanvas.toDataURL("image/png");
  
        // Create PDF with custom height based on content
        const pdfWidth = 210; // A4 width in mm
        const canvasWidth = firstCanvas.width;
        const canvasHeight = firstCanvas.height;
        const pdfHeight = (canvasHeight * pdfWidth) / canvasWidth;
  
        const pdf = new jsPDF("p", "mm", [pdfHeight, pdfWidth]);
        
        pdf.addImage(firstImgData, "PNG", 0, 0, pdfWidth, pdfHeight);
        
  
        if (send_through_email) {
          const pdfBlob = pdf.output("blob");
          setPDF_file(pdfBlob);
          // successToast("PDF Generated for email")
        } else {
          pdf.save("AI-Model-Report.pdf");
        }
  
      } catch (error) {
        errorToast("Error while downloading PDF");
        console.error(error);
      } finally {
        setloading(false);
        setemail_loader(false);
        setEmail_modal_msg("PDF successfully generated, Please enter email to send generated PDF")
      }

    }
    else{
      setloading(false)
    }
  };
const downloadPDF = async ({ send_through_email } = {}) => {
  const { filters } = data;
  const checkingFilters = validateFilters(filters, true);
  if (!checkingFilters) {
    setloading(false);
    return;
  }

  if (send_through_email) {
    toggleModal();
    setemail_loader(true);
  } else {
    setloading(true);
  }

  try {
    const el = document.getElementById("report-content");

    // 1) Canvas capture
    const canvas = await html2canvas(el, { scale: 2, useCORS: true });
    const imgData = canvas.toDataURL("image/png");

    // 2) PDF sizing
    const pdfWidth = 210; // A4 mm
    const canvasWidth = canvas.width;
    const canvasHeight = canvas.height;
    const pdfHeight = (canvasHeight * pdfWidth) / canvasWidth;

    const pdf = new jsPDF("p", "mm", [pdfWidth, pdfHeight]);

    // 3) Add the screenshot image
    pdf.addImage(imgData, "PNG", 0, 0, pdfWidth, pdfHeight);

    // 4) Centered "Back to Dashboard" TEXT with LINK (no button)
    const dashboardURL = "https://unilever.disruptlabs.tech/reports/factory"; // HTTPS URL

    // Text label + sizing
    const label = "Back to Dashboard";
    const fontSizePt = 8;                 // pt
    const ptToMm = 0.352778;              // 1pt ≈ 0.3528mm
    const textHeightMm = fontSizePt * ptToMm;

    // Keep the same vertical position used for the button before
    const padX = 8; // retained only to preserve original center math
    const padY = 4; // retained only to preserve original center math

    pdf.setFontSize(fontSizePt);
    const textWidthMm = pdf.getTextWidth(label);

    // Reconstruct the previous button box to KEEP POSITION identical
    const btnW = textWidthMm + padX * 2;
    const btnH = textHeightMm + padY * 2;
    const btnX = (pdfWidth - btnW) / 2; // centered horizontally
    const btnY = 30;                    // same vertical position as before

    // Compute the center (unchanged)
    const centerX = btnX + btnW / 2;
    const centerY = btnY + btnH / 2;

    // Place text centered at the same spot
    const textBaselineY = centerY + textHeightMm * 0.35; // baseline adjustment similar to original
    pdf.setTextColor(128, 128, 128);
    pdf.text(label, centerX, textBaselineY, { align: "center" });

    // Clickable link exactly over the TEXT (tight hotspot)
    const linkW = textWidthMm;
    const linkH = textHeightMm * 1.2; // a touch taller for easier clicking
    const linkX = centerX - linkW / 2;
    const linkY = centerY - linkH / 2;
    pdf.link(linkX, linkY, linkW, linkH, { url: dashboardURL });

    // 5) Save / email
    if (send_through_email) {
      const pdfBlob = pdf.output("blob");
      setPDF_file(pdfBlob);
    } else {
      pdf.save("AI-Model-Report.pdf");
    }
  } catch (error) {
    console.error(error);
    errorToast("Error while downloading PDF");
  } finally {
    setloading(false);
    setemail_loader(false);
    setEmail_modal_msg(
      "PDF successfully generated, Please enter email to send generated PDF"
    );
  }
};



  function handleEmailFinalSubmisstion() {

    const payload = {
      receivers: receivers,
      cc_emails: ccEmails,
      subject: "Ai Model & Reports PDF",
      body: "Testing api",
      attachment: PDF_file
    }

    Send_PDF_through_Email(payload, setemail_loader, setEmail_modal_msg, setShowSuccessMsg)

  };

  const callAllApisForReports=(filters)=>{
    const userData = JSON.parse(localStorage.getItem("userData")) || {};
    const userId = userData.id || 0;
    const factoryId = userData.factory?.id || 0;
    const currentWeek = filters.week || "";

    setloading(true);
    setemail_loader(true)

    // Use Promise.all to run both API calls in parallel
    Promise.all([
      // fetchRecordsWithFilter({ userId, factoryId, filters }),
      fetchLeaderboardData({ factoryId, week: currentWeek }),
      fetchHeatmapDataForReports(userId, factoryId, filters),
      getSubareasDetailsForPDF(userId, factoryId, filters),
      executive_summary_for_report(userId, factoryId, filters,true),
      get_alerts_for_report(userId, factoryId, filters,true),
      fetchAnalyticsBars(userId, factoryId, filters),
      get_alerts_for_report(userId, factoryId, filters)
    ])
      .then(([ leaderboardDataRes, heatmapDataRes, subareasDetailsRes, executiveSummaryRes, alertsForReportRes, analyticsBarsRes, totalAlertsRes]) => {
        // setVerifiedAlerts(verifiedAlertsRes);
        setLeaderboardData(leaderboardDataRes);
        setHeatmapData(heatmapDataRes);
        setExecutiveSummary(executiveSummaryRes);
        setProgressData(analyticsBarsRes);
        setAlertsForReports(alertsForReportRes) ;
        const finalData = setDataFormat(subareasDetailsRes?.data); 
        const sortedData = finalData?.sort((a, b) => {
          if (a.alerts !== b.alerts) {
            return b.alerts - a.alerts
          }
          // Then, sort by areaName (ascending, numerically)
          const areaNumA = parseInt(a.areaName.split('-')[1], 10);
          const areaNumB = parseInt(b.areaName.split('-')[1], 10);
          return areaNumA - areaNumB;
        }) 
        setSubAreas(sortedData)

      })
      .catch(error => {
        console.error("Error fetching data:", error);
        errorToast("Error loading report data");
      })
      .finally(() => { setloading(false); setemail_loader(false) });
  }


  useEffect(() => {
    const { filters } = data; 
    const checkingFilters= validateFilters(filters,false)
    if(checkingFilters && filters){
      console.log("-------calling shift apis-------------")
      callAllApisForReports(filters)
    }
    
  }, [data]);


  useEffect(() => {
    console.log("clear user data")
    if (modal == false) {
      setCcEmails([])
      setReceivers([])
      setEmail_modal_msg("Generating PDF...")
    }
  }, [modal])

  
  return (
    <div>
      {/* Hidden container for first page content without leaderboard */}
      <div
        id="report-content"
        style={{
          position: "fixed",
          top: 0,
          left: 0,
          width: "1000px",
          pointerEvents: "none",
          zIndex: -9999,
          background: "white",
        }}
      >
        {/* <AIModelReportComponent data={data} verifiedAlerts={verifiedAlerts} /> */}
        {!loading && <TestComponent data={data} heatmapData={heatmapData} subAreas={subAreas} executiveSummary={executiveSummary} filters={data?.filters} progressData={progressData} alertsForReports={alertsForReports} />
        }      </div>

      {/* Hidden container for leaderboard on separate page */}
      <div
        id="report-leaderboard"
        style={{
          position: "fixed",
          top: 0,
          left: 0,
          width: "1000px",
          pointerEvents: "none",
          zIndex: -9999,
          background: "white",
          marginTop: "40px",
        }}
      >
        <LeaderboardOnly leaderboardData={leaderboardData} />
      </div>

      <div style={{ textAlign: "center" }}>
        <button
          onClick={downloadPDF}
          disabled={loading}
          className={"btn btn-outline-danger"}
        >
          {loading ? (
            <div
              className="spinner-border spinner-border-sm text-danger"
              role="status"
            ></div>
          ) : (
            "Shift PDF"
          )}
        </button>
        <button
          onClick={() => {downloadPDF({ send_through_email: true }); setShowSuccessMsg(false) }}
          disabled={loading}
          className="ms-2 btn btn-outline-primary"
        >
          {email_loader ? (
            <div
              className="spinner-border spinner-border-sm text-primary"
              role="status"
            ></div>
          ) : (
            "Email"
          )}
        </button>
      </div>
      <AIModal toggleModal={toggleModal}
        modal={modal}
        Email_modal_msg={Email_modal_msg}
        receivers={receivers}
        setReceivers={setReceivers}
        ccEmails={ccEmails}
        setCcEmails={setCcEmails}
        handleSubmit={handleEmailFinalSubmisstion}
        // submit btn will be disable until pdf is generating
        email_loader={email_loader}
        sub={ShowSuccessMsg}
      />
    </div>
  );
};

export default DownloadAIModelReport;


