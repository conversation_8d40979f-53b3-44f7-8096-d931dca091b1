import AreaService from "../api/areaService";

export const ResetHelper = async (email) => {
    try {
        const payload = { email };
        console.log("Sending password reset request with:", payload);
        const response = await AreaService.getForgotPassword(payload);
        
        // Check if response is a Response object (from fetch)
        if (response instanceof Response) {
            if (!response.ok) {
                const errorText = await response.text();
                let errorMessage;
                try {
                    // Try to parse as JSON
                    const errorData = JSON.parse(errorText);
                    errorMessage = errorData.message || errorData.error || "Unknown error occurred";
                } catch (e) {
                    // If not JSON, use text as is
                    errorMessage = errorText || "Unknown error occurred";
                }
                throw new Error(errorMessage);
            }
            // Success - return the response for further handling if needed
            return response;
        } else {
            // If AreaService returns something that's not a Response object
            console.log("Response received:", response);
            return response;
        }
    } catch (error) {
        console.error("Error in password reset:", error.message);
        throw error; // Rethrow for the calling component to handle
    }
};

export const UpdatePassword = async (token, new_password) => {
    try {
        const payload = { token, new_password };
        console.log("Updating password with:", payload);
        const response = await AreaService.getUpdatePassword(payload);
        
        // Check if response is a Response object
        if (response instanceof Response) {
            if (!response.ok) {
                const errorText = await response.text();
                let errorMessage;
                try {
                    const errorData = JSON.parse(errorText);
                    errorMessage = errorData.message || errorData.error || "Failed to update password";
                } catch (e) {
                    errorMessage = errorText || "Failed to update password";
                }
                throw new Error(errorMessage);
            }
            return await response.json();
        } else {
            return response;
        }
    } catch (error) {
        console.error("Error updating password:", error.message);
        throw error;
    }
}