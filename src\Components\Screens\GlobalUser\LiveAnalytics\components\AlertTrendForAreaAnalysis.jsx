import React, { useContext, useEffect, useState } from 'react';
import { format, startOfWeek, addDays, parseISO, setISOWeek, getYear } from 'date-fns';
import { Card, CardBody } from 'react-bootstrap'
import Chart from "react-apexcharts";
import './alertTrend.css'
import Loader3 from '../../../../../CommonElements/Spinner/loader3'
import liveanalyticcontext from '../../../../../_helper/formData/LiveAnalytics/LiveAnalytics'
import AreaService from '../../../../../api/areaService';
import NoDataComp from '../../../../../Components/Common/NoDataComp/NoDataComp';

const AlertTrendForAreaAnalysis = ({ ref, filters, callApi }) => {
    console.log('filtersff', filters?.week)
    const [chartData, setChartData] = useState({
        series: [],
        options: {
            chart: {
                type: 'line',
                height: 350,
                toolbar: {
                    show: false
                },
                zoom: {
                    enabled: false
                }
            },
            stroke: {
                curve: 'smooth',
                width: 2
            },
            colors: ['#1E3A8A', '#d97706', '#049aec', "#6B21A8", "#B91C1C", "#54ba4a", "#FF6384", "#36A2EB", "#FFCE56"],
            // dataLabels: {
            //     enabled: true,
            //     style: {
            //         fontSize: '10px',
            //         colors: ["#000"]
            //     }
            // },
            dataLabels: {
                enabled: true,
                formatter: function (val) {
                    return val === 0 ? '' : val;
                },
                style: {
                    fontSize: '8px',
                    colors: ["#000"]
                }
            },

            markers: {
                size: 4,
                hover: {
                    size: 6
                }
            },
            xaxis: {
                categories: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
                labels: {
                    rotate: -45,
                    style: {
                        fontSize: '12px'
                    }
                }
            },
            yaxis: {
                title: {
                    text: 'Alerts',
                    style: {
                        fontSize: '14px'
                    }
                },
                min: 0,
                forceNiceScale: true,
                labels: {
                    formatter: function (value) {
                        return Math.round(value);
                    }
                }
            },
            legend: {
                position: 'bottom',
                horizontalAlign: 'center',
                fontSize: '12px'
            },
            tooltip: {
                shared: true,
                intersect: false
            }
        }
    });

    const [chartLoader, setChartLoader] = useState(true);
    const { setalerttrenddatacontext } = useContext(liveanalyticcontext);
    const [totalAlerts, setTotalAlerts] = useState(0);
    const [hasData, setHasData] = useState(true);





    const getDatesOfWeek = (weekString) => {
        // weekString example: "2025-W18"
        if (!weekString) return [];

        // Extract year and week number
        const [yearStr, weekStr] = weekString.split('-W');
        const year = parseInt(yearStr, 10);
        const week = parseInt(weekStr, 10);

        // Create a date for Jan 4th of the year (guarantees week 1 is correct)
        const jan4 = new Date(year, 0, 4);

        // Set ISO week and get the first day (Monday)
        // Using date-fns setISOWeek to get the Monday of the given ISO week
        const firstDayOfWeek = startOfWeek(setISOWeek(jan4, week), { weekStartsOn: 1 }); // Monday

        // Generate 7 days starting from Monday
        const days = [];
        for (let i = 0; i < 7; i++) {
            const date = addDays(firstDayOfWeek, i);
            // Format: 'Mon 05 May' or just '05 May'
            days.push(format(date, 'EEE dd MMM')); // Example: "Mon 05 May"
        }

        return days;
    };

    // Function to fetch data from API
    const fetchAlertTrendData = async () => {
        setChartLoader(true);

        const payload = {
            weekly: filters?.week,
            month: filters?.month,
            shift: filters?.shifts,
            factory_id: JSON.parse(localStorage.getItem('userData'))?.factory?.id || 0,
            user_id: JSON.parse(localStorage.getItem('userData'))?.id || 0,
            start_date: filters?.starting || filters.date || '' ,
            end_date: filters?.ending || filters.date || '',

        };

        try {
            const response = await AreaService.alert_trend_live_analytics_by_area(payload);

            if (response && response.data && response.data.heatmapData) {
                processApiData(response.data.heatmapData);
            } else {
                console.error('Invalid API response format');
                setHasData(false);
            }
        } catch (error) {
            console.error('Error fetching alert trend data:', error);
            setHasData(false);
        } finally {
            setChartLoader(false);
        }
    };

    // Process API data
    const processApiData = (heatmapData) => {
        if (!heatmapData || !heatmapData.categories || !heatmapData.data) {
            console.error('Invalid heatmap data format');
            setHasData(false);
            return;
        }

        try {
            // Extract categories (days of the week)
            // const categories = heatmapData.categories;
            const categories = filters?.week ? getDatesOfWeek(filters.week) : heatmapData.categories;

            // Extract series data (each area is a line)
            const seriesData = heatmapData.data.filter(item => item.name !== 'Total');

            // Calculate total alerts
            const totalAlertsCount = seriesData.reduce((total, series) => {
                return total + series.data.reduce((sum, value) => sum + value, 0);
            }, 0);

            setTotalAlerts(totalAlertsCount);

            // Check if we have any data
            const hasAnyData = seriesData.some(series =>
                series.data.some(value => value > 0)
            );

            setHasData(hasAnyData);

            // Update context if needed
            setalerttrenddatacontext(seriesData);

            // Update chart data
            setChartData({
                ...chartData,
                series: seriesData,
                options: {
                    ...chartData.options,
                    xaxis: {
                        ...chartData.options.xaxis,
                        categories: categories
                    }
                }
            });
        } catch (error) {
            console.error('Error processing API data:', error);
            setHasData(false);
        }
    };

    // Fetch data when filters change
    useEffect(() => {
        fetchAlertTrendData();
    }, [callApi]);

    return (
        <Card ref={ref} style={{ borderRadius: '32px', height: '399px' }}>
            <h5 className="px-4 py-3" style={{ fontWeight: '500', color: '#383838', }}>Alerts Trend  <div>
                <span style={{ fontSize: '15px' }}>Total Alerts: {chartLoader ? '...' : totalAlerts}</span>
            </div></h5>

            {chartLoader ? (
                <div className='w-100 h-100 d-flex justify-content-center align-items-center position-absolute' style={{ height: '100%' }}>
                    <Loader3 />
                </div>
            ) : !hasData ? (
                <div className="d-flex justify-content-center align-items-center" style={{ height: '320px' }}>
                    <div className="text-center">
                        <div className="mb-3">
                            <span style={{ backgroundColor: '#54BA4A', color: 'white', borderRadius: '50%', width: '40px', height: '40px', display: 'inline-flex', justifyContent: 'center', alignItems: 'center', marginBottom: '15px' }}>
                                <i className="fa fa-check" style={{ fontSize: '24px' }}></i>
                            </span>
                        </div>
                        <h3 style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '10px' }}>All Clear!</h3>
                        <p style={{ fontSize: '14px', marginBottom: '10px' }}>
                            No alerts detected for this time period.
                        </p>
                    </div>
                </div>
            ) : (
                <CardBody className="responsive-scroll">
  <div className="chart-wrapper">
    <Chart
      options={chartData.options}
      series={chartData.series}
      type="line"
      height={'268px'}
    />
  </div>
</CardBody>




            )}
        </Card>
    )
}

export default AlertTrendForAreaAnalysis





