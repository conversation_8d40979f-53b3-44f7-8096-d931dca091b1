import React, { useEffect, useMemo, useState } from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>dal<PERSON>ody,
    ModalFooter,
    Form,
    FormGroup,
    Label,
    Input,
    Card,
    CardBody,
} from "reactstrap";
import DataTable from "react-data-table-component";
import { errorToast, successToast } from "../../../../../_helper/helper";
import itService from "../../../../../api/itService";
import { CiEdit } from "react-icons/ci";

const uid = () => Math.random().toString(36).slice(2, 9);

export default function ReleaseNotes() {
    const [isOpen, setIsOpen] = useState(false);
    const [version, setVersion] = useState("");
    const [noteText, setNoteText] = useState("");
    const [isSaving, setIsSaving] = useState(false);
    const [filterText, setFilterText] = useState("");
    const [editMode, seteditMode] = useState(false);
    const [selectedNote, setselectedNote] = useState({})

    const [notes, setNotes] = useState([]);

    const toggle = () => setIsOpen((s) => !s);

    const handleAddNotes = () => {
        setNoteText("");
        setVersion("");
        seteditMode(false)
        toggle();
    };

    const handleSave = async (e) => {
        e.preventDefault();
        if (!noteText.trim() || !version.trim()) return;

        try {
            setIsSaving(true);
            const payload = {
                version: version.trim(),
                note: noteText.trim(),
            };
            const res = editMode ? await itService.update_release_notes(selectedNote.id, payload) : await itService.add_release_notes(payload);

            if (res.status === 201 || res.status === 200) {
                successToast(`Release note ${editMode ? 'updated' : 'added'}`);
                if (editMode) {
                    const filteredNote = notes.filter(item => item.id !== selectedNote.id);
                    setNotes([...filteredNote, res.data.data])
                } else {
                    setNotes((prev) => [
                        res.data.data,
                        ...prev,
                    ]);
                }
            }
            toggle();
        } finally {
            setIsSaving(false);
        }
    };

    const columns = useMemo(
        () => [
            {
                name: "Version",
                selector: (row) => row.version,
                sortable: true,
                // maxWidth: "140px",
                cell: (row) => (
                    <span className="badge text-bg-primary rounded-pill px-3 py-2">
                        {row.version}
                    </span>
                ),
            },
            {
                name: "Date",
                selector: (row) => row.date || row.created_at,
                sortable: true,
                // maxWidth: "200px",
                cell: (row) => {
                    const raw = row.date || row.created_at;
                    if (!raw) return "—";
                    // Ensure microseconds are removed for safe parsing
                    const clean = raw.replace(/\.\d{1,6}/, "");
                    // Parse as UTC and convert to local
                    const d = new Date(clean + "Z");
                    return isNaN(d) ? raw : d.toLocaleString();
                },
            },


            {
                name: "Notes",
                selector: (row) => row.note,
                // grow: 2,
                wrap: true,
            },
            {
                name: "Action",
                cell: (row) => (
                    <button onClick={() => handleEditNote(row)} style={{ border: 'none', background: 'none' }}>
                        <CiEdit size={20} />
                    </button>
                ),
            },
        ],
        []
    );

    const filteredItems = useMemo(() => {
        const q = filterText.trim().toLowerCase();
        if (!q) return notes;
        return notes.filter(
            (n) => n.version.toLowerCase().includes(q) || n.note.toLowerCase().includes(q)
        );
    }, [notes, filterText]);

    const customStyles = {
        table: {
            style: { borderRadius: "1rem", overflow: "hidden" },
        },
        headRow: {
            style: {
                backgroundColor: "#f8f9fa",
                minHeight: "56px",
                fontWeight: 600,
            },
        },
        rows: {
            style: {
                minHeight: "56px",
            },
            highlightOnHoverStyle: {
                backgroundColor: "#f5f9ff",
                transition: "background-color 0.25s ease",
            },
        },
        cells: {
            style: {
                fontSize: "0.95rem",
            },
        },
        pagination: {
            style: {
                borderTop: "1px solid #e9ecef",
            },
        },
    };

    const subHeader = (
        <div className="d-flex gap-2 align-items-center w-100">
            <Input
                placeholder="Search version or text…"
                value={filterText}
                onChange={(e) => setFilterText(e.target.value)}
            />
            <Button color="secondary" outline onClick={() => setFilterText("")}>Clear</Button>
        </div>
    );

    const handleEditNote = (row) => {
        setVersion(row.version);
        setNoteText(row.note);
        seteditMode(true);
        setselectedNote(row)
        toggle();
    };
    const getReleaseNotes = async () => {
        try {
            const res = await itService.get_all_release_notes()
            if (res.status === 200) {
                setNotes(res?.data)
            }
        } catch (error) {
            errorToast('Error while fetching release notes')
        }
    }

    useEffect(() => {

        getReleaseNotes()

    }, [])

    return (
        <Container fluid className="py-4">
            <Card className="shadow-sm border-0 rounded-4">
                <CardBody>
                    <div className="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h4 className="mb-0">Release Notes</h4>
                            <small className="text-muted">Track changes and ship confidently</small>
                        </div>
                        <div className="d-flex gap-2">
                            <Button color="primary" className="rounded-pill" onClick={handleAddNotes}>
                                + Add Release Note
                            </Button>
                        </div>
                    </div>

                    <DataTable
                        columns={columns}
                        data={filteredItems}
                        pagination
                        highlightOnHover
                        persistTableHead
                        customStyles={customStyles}
                        subHeader
                        subHeaderComponent={subHeader}
                        responsive
                    />
                </CardBody>
            </Card>

            <Modal isOpen={isOpen} toggle={toggle} centered>
                <ModalHeader toggle={toggle}>Add Release Note</ModalHeader>
                <Form onSubmit={handleSave}>
                    <ModalBody>
                        <FormGroup>
                            <Label for="version">Version</Label>
                            <Input
                                id="version"
                                type="text"
                                placeholder="e.g., v1.3.0"
                                value={version}
                                onChange={(e) => setVersion(e.target.value)}
                                required
                            />
                        </FormGroup>
                        <FormGroup>
                            <Label for="noteText">Details</Label>
                            <Input
                                id="noteText"
                                type="textarea"
                                rows={6}
                                placeholder="What changed? (e.g., Fixed login redirect, improved charts, etc.)"
                                value={noteText}
                                onChange={(e) => setNoteText(e.target.value)}
                                required
                            />
                            <small className="text-muted d-block mt-2">
                                Tip: Keep it concise. You can add bullet points with dashes.
                            </small>
                        </FormGroup>
                    </ModalBody>
                    <ModalFooter className="d-flex justify-content-between">
                        <Button type="button" color="link" onClick={toggle} className="text-muted">
                            Cancel
                        </Button>
                        <Button color="primary" type="submit" disabled={isSaving || !noteText.trim() || !version.trim()}>
                            {isSaving ? "Saving…" : "Save Note"}
                        </Button>
                    </ModalFooter>
                </Form>
            </Modal>
        </Container>
    );
}