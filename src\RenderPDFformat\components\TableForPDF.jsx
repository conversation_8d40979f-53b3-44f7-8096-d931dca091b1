import React from "react";

const SimpleTable = ({ 
  title, 
  columns = [],   // [{ header: 'Name', key: 'name', align: 'left'|'center'|'right' }]
  data = [] 
}) => {
  if (!data || data.length === 0) {
    return <p style={{ textAlign: "center", color: "#777", marginTop: "20px" }}>No data available</p>;
  }

  return (
    <div className="border px-3 b-r-10 py-3" style={{ marginBottom: "30px" }}>
      
      {title && (
        <h3 style={{ fontSize: "16px", fontWeight: "bold", marginBottom: "15px" }}>
          {title}
        </h3>
      )}
      <table style={{ width: "100%", borderCollapse: "collapse", fontSize: "13px" }}>
        <thead>
          <tr>
            {columns.map(({ header, key, width, align = "left" }, i) => (
              <th
                key={i}
                style={{
                  padding: "10px",
                  textAlign: align,
                  borderBottom: "1px solid #ddd",
                  width: width || "auto",
                }}
              >
                {header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((row, rowIndex) => (
            <tr
              key={rowIndex}
              style={{
                borderBottom:
                  rowIndex === data.length - 1 ? "none" : "1px solid #ddd",
              }}
            >
              {columns.map(({ key, align = "left" }, colIndex) => (
                <td
                  key={colIndex}
                  style={{
                    padding: "10px",
                    textAlign: align,
                  }}
                >
                  {/* Support nested keys or custom render */}
                  {typeof key === "function" ? key(row, rowIndex) : row[key]}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default SimpleTable;
