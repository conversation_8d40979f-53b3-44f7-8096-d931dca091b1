import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  Modal<PERSON>ooter,
  Form,
  FormGroup,
  Label,
  Input,
  Spinner,
} from "reactstrap";

const EmailModal = ({Toggle:toggle, modal, handleSubmitSendEmail, emailLoader}) => {
  
  const [selectedEmail, setSelectedEmail] = useState("");
  const [message, setMessage] = useState("");

  const role = JSON.parse(localStorage.getItem('role'))

  

//   const handleSubmit = (e) => {
//     e.preventDefault();
//     console.log("Selected Email:", selectedEmail);
//     console.log("Message:", message);

//     toggle(); // Close modal after submission
//   };

  return (
    <div>
   
      <Modal isOpen={modal} toggle={toggle}>
        <ModalHeader toggle={toggle}>Send a Message</ModalHeader>
        <Form onSubmit={(e)=> handleSubmitSendEmail(e ,selectedEmail, message)}>
          <ModalBody>
            <FormGroup>
              <Label for="emailDropdown">Select Email</Label>
              <Input
                type="select"
                id="emailDropdown"
                value={selectedEmail}
                onChange={(e) => setSelectedEmail(e.target.value)}
              >
                <option value="">Select an email</option>
                <option value="<EMAIL>"><EMAIL></option>
                <option value="<EMAIL>">
                  <EMAIL>
                </option>
                <option value="<EMAIL>">
                  <EMAIL>
                </option>
              </Input>
            </FormGroup>

      

            <FormGroup>
              <Label for="messageText">Message</Label>
              <Input
                type="textarea"
                id="messageText"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                rows={5}
              />
            </FormGroup>
           

          </ModalBody>

          <ModalFooter>
            <Button color="primary" type="submit">
              {/* Submit {emailLoader && <Spinner />} */}
              {emailLoader ? (
                <>
                  Submitting <Spinner size="sm" color="light" />
                </>
              ) : (
                "Submit"
              )}
            </Button>
            <Button color="secondary" onClick={toggle}>
              Cancel
            </Button>
          </ModalFooter>
        </Form>
      </Modal>
    </div>
  );
};

export default EmailModal;