import React from 'react'
import { errorToast, successToast } from '../../../_helper/helper'

export default function Index({heading, imgData}) { 
    const handleClick= async (data)=>{ 
        const userData=JSON.parse(localStorage.getItem('userData'))?.email 
        const user_factory_id=userData == "<EMAIL>" ? 2 : userData == "<EMAIL>" ? 1 : null
        const payload={
            images:[
                {
                    client_id:1,
                    factory_id: user_factory_id,
                    image_url:data.unannotated_image,
                    detection_model:"yolo",
                    camera_id:data.camera_id || ""
                 }
            ]
        }
        const res=await fetch('https://beannotation.disruptlabs.tech/annotation/insert_unannotated_images',{
            method:'POST',
            headers:{
                'Content-Type':'application/json'
            },
            body:JSON.stringify(payload)
        })
        if(res.status==200){
            successToast('Image sent for annotation')
        }
        else{
            errorToast('Error while sending image for annotation')
        }
    }
  return (
    <div>
      <button className='btn btn-primary' onClick={()=>{handleClick(imgData)}}>{heading}</button>
    </div>
  )
}
