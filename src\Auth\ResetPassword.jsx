import { useState, useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { Card, CardBody, CardHeader, Form, FormGroup, Label, Input, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>Footer } from "reactstrap";
import { UpdatePassword } from "./ResetHelper";
import { Eye, EyeOff } from "lucide-react";
import './Resetpassword.css'

const NewPassword = () => {
  // Get token from URL
  const location = useLocation();
  const navigate = useNavigate();
  const queryParams = new URLSearchParams(location.search);
  const token = queryParams.get("token");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState("");
  const [isFocused, setIsFocused] = useState({ password: false, confirm: false });
  const [isHovered, setIsHovered] = useState({ password: false, confirm: false });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);


  // Animation effect for the card
  useEffect(() => {
    const card = document.querySelector(".reset-card");
    setTimeout(() => {
      card?.classList.add("show");
    }, 100);

    // Validate token exists
    if (!token) {
      setError("Invalid or missing reset token. Please request a new password reset link.");
    }
  }, [token]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");

    // Password validation
    if (!password) {
      setError("Please enter a new password");
      shakeInput(".password-input");
      return;
    }

    // Check for minimum length (8 characters)
    if (password.length < 8) {
      setError("Password must be at least 8 characters long");
      shakeInput(".password-input");
      return;
    }

    // Check for at least one uppercase letter
    if (!/[A-Z]/.test(password)) {
      setError("Password must contain at least one uppercase letter");
      shakeInput(".password-input");
      return;
    }

    // Check for at least one special character
    if (!/[^a-zA-Z0-9]/.test(password)) {
      setError("Password must contain at least one special character");
      shakeInput(".password-input");
      return;
    }

    // Check for at least one number
    if (!/\d/.test(password)) {
      setError("Password must contain at least one number");
      shakeInput(".password-input");
      return;
    }

    // Confirm password match
    if (password !== confirmPassword) {
      setError("Passwords do not match");
      shakeInput(".confirm-password-input");
      return;
    }

    setIsSubmitting(true);

    try {
      await UpdatePassword(token, password);
      setIsSuccess(true);

      // Add success animation
      const successIcon = document.querySelector(".success-icon");
      successIcon?.classList.add("pulse");
    } catch (err) {
      setError(err.message || "Failed to reset password. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };
  const shakeInput = (selector) => {
    const input = document.querySelector(selector);
    input?.classList.add("shake");
    setTimeout(() => {
      input?.classList.remove("shake");
    }, 500);
  };

  return (
    <div className="reset-password-container">
      <div className="floating-shapes">
        <div className="shape shape-1"></div>
        <div className="shape shape-2"></div>
        <div className="shape shape-3"></div>
        <div className="shape shape-4"></div>
      </div>

      <Card className="reset-card">
        <CardHeader className="text-center border-0 bg-transparent">
          <div className="logo-container">
            <div className="logo-circle">
              <i className="lock-icon"></i>
            </div>
          </div>
          <h3 className="card-title">Set New Password</h3>
          <p className="card-subtitle">
            Create a new secure password for your account
          </p>
        </CardHeader>

        <CardBody>
          {isSuccess ? (
            <div className="success-container text-center">
              <div className="success-icon">
                <i className="check-icon"></i>
              </div>
              <h3 className="mt-4">Password Updated!</h3>
              <p className="success-message">
                Your password has been successfully reset. You can now log in with your new password.
              </p>
            </div>
          ) : (
            <Form onSubmit={handleSubmit}>
              <FormGroup>
                <Label for="password" className="form-label">
                  New Password
                </Label>
                <div
                  className={`input-container ${isFocused.password ? "focused" : ""
                    } ${isHovered.password ? "hovered" : ""}`}
                >
                  <i className="password-icon"></i>
                  {showPassword ? (
                    <EyeOff className="eye-icon" onClick={() => setShowPassword(false)} />
                  ) : (
                    <Eye className="eye-icon" onClick={() => setShowPassword(true)} />
                  )}
                  <Input
                    type={showPassword ? "text" : "password"}
                    name="password"
                    id="password"
                    className="password-input custom-input"
                    placeholder="Enter your new password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    onFocus={() => setIsFocused({ ...isFocused, password: true })}
                    onBlur={() => setIsFocused({ ...isFocused, password: false })}
                    onMouseEnter={() => setIsHovered({ ...isHovered, password: true })}
                    onMouseLeave={() => setIsHovered({ ...isHovered, password: false })}
                    required
                  />
                </div>
              </FormGroup>

              <FormGroup>
                <Label for="confirmPassword" className="form-label">
                  Confirm Password
                </Label>
                <div
                  className={`input-container ${isFocused.confirm ? "focused" : ""
                    } ${isHovered.confirm ? "hovered" : ""}`}
                >
                  <i className="password-icon"></i>
                  {showConfirmPassword ? (
                    <EyeOff className="eye-icon" onClick={() => setShowConfirmPassword(false)} />
                  ) : (
                    <Eye className="eye-icon" onClick={() => setShowConfirmPassword(true)} />
                  )}
                  <Input
                    type={showConfirmPassword ? "text" : "password"}
                    name="confirmPassword"
                    id="confirmPassword"
                    className="confirm-password-input custom-input"
                    placeholder="Confirm your new password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    onFocus={() => setIsFocused({ ...isFocused, confirm: true })}
                    onBlur={() => setIsFocused({ ...isFocused, confirm: false })}
                    onMouseEnter={() => setIsHovered({ ...isHovered, confirm: true })}
                    onMouseLeave={() => setIsHovered({ ...isHovered, confirm: false })}
                    required
                  />
                </div>

                {error && (
                  <Alert color="danger" className="error-message mt-3">
                    <i className="error-icon"></i> {error}
                  </Alert>
                )}
              </FormGroup>

              <Button
                color="primary"
                block
                className="submit-button"
                disabled={isSubmitting}
                type="submit"
              >
                {isSubmitting ? (
                  <>
                    <Spinner size="sm" className="mr-2" /> Updating...
                  </>
                ) : (
                  "Set New Password"
                )}
              </Button>
            </Form>
          )}
        </CardBody>

        <CardFooter className="text-center border-0 bg-transparent">
          {isSuccess ? (
            <Button
              color="link"
              className="back-button"
              onClick={() => navigate("/login")}
            >
              <a href="/login" style={{ color: "#e56e3b" }}>Back to Login</a>
            </Button>
          ) : (
            <Link to={`${process.env.PUBLIC_URL}/login`} >Remember your password?Login</Link>
          )}
        </CardFooter>
      </Card>
    </div>
  );
};

export default  NewPassword;