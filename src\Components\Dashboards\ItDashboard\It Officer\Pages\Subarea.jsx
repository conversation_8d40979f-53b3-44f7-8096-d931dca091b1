import React, { useEffect, useState } from 'react'
import { Container, Input, FormGroup, Label, Card, CardHeader, CardBody, Spinner, Button, Media } from 'reactstrap'
import { errorToast, successToast } from '../../../../../_helper/helper'; 
import FactoryService from '../../../../../api/factoryService';
import DataTable from 'react-data-table-component'; 
import { FiMap, FiLayers, FiHome, FiGrid } from 'react-icons/fi';
import { MdOutlineDashboard } from 'react-icons/md';
import AddSubAreaModal from './AddSubAreaModal';
import { StyledCard, StyledSelect, EmptyStateContainer, EmptyStateIcon, StatsCard, StatsIcon, StatsContent, StatsTitle, StatsValue } from './styledCards';
import itService from '../../../../../api/itService';
import ConfirmationModal from '../Components/ConifrmationModal/Confirmation_Modal';
 
export default function SubArea() {
  const [subAreas, setSubAreas] = useState([])
  const [factories, setFactories] = useState([])
  const [selectedFactory, setSelectedFactory] = useState(null)
  const [loading, setLoading] = useState(false)
  const [addModalOpen, setAddModalOpen] = useState(false);
  const [addLoading, setAddLoading] = useState(false); 
  const [modaltype, setmodaltype] = useState('add');
  const [showconfirmationModal, setshowconfirmationModal] = useState(false)
  const taggle_confirmation_modal = () => setshowconfirmationModal(!showconfirmationModal) 
  const [confirmation_modal_data, setconfirmation_modal_data] = useState();
  const [form, setForm] = useState({
      area_id: '',
      name: '',
      address: '',
      file: null,
  });
  async function getAllSubarea() {
    setLoading(true);
    try {
      const res = await FactoryService.getAllFactories();
      setFactories(res.data?.data || [])
    } catch (error) { 
      errorToast('Error while fetching data')
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    getAllSubarea()
  }, [])

  const handleFactoryChange = (e) => {
    const factoryId = e.target.value
    const factoryObj = factories.find(f => f.factory_id == factoryId) 
    setSelectedFactory(factoryObj)
    if (factoryObj && Array.isArray(factoryObj.areas)) { 
      const subAreaList = factoryObj.areas.flatMap(area =>
        (area.sub_area || []).map(sub => ({
          area_name: area.area,
          area_id: area.id,
          factory_name: factoryObj.name,
          factory_id: factoryObj.factory_id,
          sub_area_id: sub.id,
          sub_area_name: sub.name,
          sub_area_status:sub.active
        }))
      ); 
      setSubAreas(subAreaList); 
    } else {
      setSubAreas([]);
    }
  }

  // Helper to get all areas for the selected factory
  const areaOptions = selectedFactory?.areas || [];

  const handleAddSubArea = async (form, resetForm) => {
    setAddLoading(true);
    try {
      const user_id = JSON.parse(localStorage.getItem('userData'))?.id;
      const formData = new FormData();
      formData.append('user_id', user_id);
      formData.append('area_id', form.area_id);
      formData.append('name', form.name);
      formData.append('address', form.address);
      if (form.file) formData.append('file', form.file); 
      await FactoryService.addSubArea(formData);
      successToast('Sub-Area added successfully!');
      setAddModalOpen(false);
      resetForm();
      // Refresh sub-areas
      // handleFactoryChange({ target: { value: selectedFactory.factory_id } });
    } catch (error) {
      errorToast(error.message || 'Error while adding sub-area');
      setAddLoading(false);
    }
    setAddLoading(false);
  }; 
  const customStyles = {
    headRow: {
      style: {
        borderTop: '1px solid #f3f2f7',
        borderBottom: '1px solid #f3f2f7',
        fontWeight: 600,
        fontSize: '0.9rem',
        color: '#5e5873',
      },
    },
    rows: {
      style: {
        fontSize: '0.9rem',
        '&:not(:last-of-type)': {
          borderBottom: '1px solid #f3f2f7',
        },
        '&:hover': {
          backgroundColor: '#f8f7fa !important',
        },
      },
    },
  };

  const columns = [
    {
      name: 'Factory Name',
      selector: row => row.factory_name,
      sortable: true,
      cell: row => <div className="font-weight-bold">{row.factory_name}</div>,
    },
    {
      name: 'Area Name',
      selector: row => row.area_name,
      sortable: true,
      cell: row => <div>{row.area_name}</div>,
    },
    {
      name: 'Status',
      cell: row => (
        <div className="d-flex align-items-center gap-2"> 
                <Media body className={`text-end switch-size d-flex justify-content-center align-items-center  `} style={{ width: '100%' }}>
                    <Label className="switch mt-2" style={{ marginLeft: '15px' }}>
                        <Input
                            type="checkbox"
                            checked={row.sub_area_status}
                            onChange={() => {setconfirmation_modal_data(row); taggle_confirmation_modal()} } 
                        />
                        <span className={`switch-state`} style={{ height: '25px', width: '45px' }}></span>
                    </Label>
                </Media>
        </div>
      ),
      ignoreRowClick: true, 
      button: true,
    },
    {
      name: 'Sub Area Name',
      selector: row => row.sub_area_name,
      sortable: true,
      cell: row => (
        <div className="d-flex align-items-center text-center"> 
          <div className="badge badge-light-primary">
            <FiLayers size={14} />
          </div>
          <span className='ms-2'>{row.sub_area_name}</span>
        </div>
      ),
    }, 
 
    {
      name: 'Update',
      cell: row => (
        <Button
          color="primary"
          size="sm"
          onClick={() => {setForm({
            area_id: row.area_id || null,
            name: row.sub_area_name || '',
            address: row.address || '',
            file: null,
            sub_area_id:row.sub_area_id
          }); setmodaltype('edit'); toggleModal()}} 
        >
          Update Details
        </Button>
      ), 
    },
  ];

  const toggleModal = () => setAddModalOpen(!addModalOpen); 

  const handleEditSubarea = () => { 
    const user_id = JSON.parse(localStorage.getItem('userData'))?.id;
    const formData = new FormData();
    formData.append('user_id', user_id);
    formData.append('area_id', form.area_id);
    formData.append('name', form.name);
    formData.append('address', form.address);
    formData.append('sub_area_id', form.sub_area_id); // <-- Add this line
    if (form.file) formData.append('file', form.file); 
    itService.update_subarea_details(formData).then((res) => {
      if(res.status == 200) {
        successToast('Sub-Area updated successfully');
        toggleModal()
        setForm({
          area_id: '',
          name: '',
          address: '',
          file: null,
          sub_area_id: '', // reset
        });
        setmodaltype('add');
        handleFactoryChange({ target: { value: selectedFactory.factory_id } });
      }
    }).catch((err) => {
      errorToast('Error while updating sub-area');
      toggleModal()
      setForm({
        area_id: '',
        name: '',
        address: '',
        file: null,
        sub_area_id: '',
      });
      setmodaltype('add');
      console.log(err)
    })
}; 

const handlesubareastatus=()=>{
  itService.update_subarea_status(confirmation_modal_data?.sub_area_id).then((res) => {
    if(res.status == 200) {
      successToast('Status updated');
      handleFactoryChange({ target: { value: selectedFactory.factory_id } });
      taggle_confirmation_modal();
      const updatedSubAreas = subAreas.map(subArea => {
        if (subArea.sub_area_id === confirmation_modal_data.sub_area_id) {
          return { ...subArea, sub_area_status: !subArea.sub_area_status };
        }
        return subArea;
      });
      setSubAreas(updatedSubAreas);
    }
  }).catch((err) => {
    errorToast('Error while updating status');
    taggle_confirmation_modal();
  })
}
  return (
    <Container fluid >
      <div className="d-flex justify-content-between align-items-center py-3 flex-wrap">
        <h4 className="mb-0">
          {/* <FiMap className="mr-2" size={20} /> */}
          Sub-Area Management
        </h4>
        <FormGroup className="mb-0" style={{ minWidth: '250px' }}>
          <Label for="factorySelect" className="form-label">Select Factory</Label>
          <StyledSelect
            type="select"
            id="factorySelect"
            value={selectedFactory?.factory_id || ''}
            onChange={handleFactoryChange}
            disabled={loading}
          >
            <option value="">{loading ? 'Loading factories...' : 'Select Factory'}</option>
            {factories.map((factory) => (
              <option key={factory.factory_id} value={factory.factory_id}>
                {factory.name}
              </option>
            ))}
          </StyledSelect>
        </FormGroup>
      </div>

      {selectedFactory && (
        <div className="row mb-4">
          <div className="col-md-4">
            <StatsCard>
              <StatsIcon color="#7367f0">
                <FiHome />
              </StatsIcon>
              <StatsContent>
                <StatsTitle>Selected Factory</StatsTitle>
                <StatsValue>{selectedFactory.name}</StatsValue>
              </StatsContent>
            </StatsCard>
          </div>
          <div className="col-md-4">
            <StatsCard>
              <StatsIcon color="#28c76f">
                <MdOutlineDashboard />
              </StatsIcon>
              <StatsContent>
                <StatsTitle>Total Areas</StatsTitle>
                <StatsValue>{selectedFactory.areas?.length || 0}</StatsValue>
              </StatsContent>
            </StatsCard>
          </div>
          <div className="col-md-4">
            <StatsCard>
              <StatsIcon color="#ff9f43">
                <FiLayers />
              </StatsIcon>
              <StatsContent>
                <StatsTitle>Total Sub-Areas</StatsTitle>
                <StatsValue>{subAreas.length}</StatsValue>
              </StatsContent>
            </StatsCard>
          </div>
        </div>
      )}

      <StyledCard>
        <CardHeader className="d-flex justify-content-between align-items-center">
          <div>
            <h5>Sub-Area List</h5>
           {selectedFactory && ( 
              <p className="badge badge-light-primary mx-0 mb-0 mt-2" style={{fontSize:'12px'}}>
              Showing results for: {selectedFactory.name}
            </p> 
          )}
          </div>
          {selectedFactory && ( 
            <Button color="primary" onClick={() => {toggleModal(); setmodaltype('add'); setForm({
              area_id: '',
              name: '',
              address: '',
              file: null,
            })}}>
              Add Sub-Area
            </Button>  
          )}
        </CardHeader>
        <CardBody>
          {loading ? (
            <div className="d-flex justify-content-center py-5">
              <Spinner color="primary" />
            </div>
          ) : subAreas.length <= 0 ? (
            <EmptyStateContainer>
              <EmptyStateIcon>
                <FiGrid size={48} />
              </EmptyStateIcon>
              <h4 className="mb-1">
                {selectedFactory ? 'No Sub-Areas Found' : 'Select a Factory'}
              </h4>
              <p className="text-muted mb-0">
                {selectedFactory
                  ? 'The selected factory has no sub-areas defined.'
                  : 'Please select a factory to view its sub-areas.'}
              </p>
            </EmptyStateContainer>
          ) : (
            <DataTable
              columns={columns}
              data={subAreas}
              pagination
              highlightOnHover
              striped
              noHeader
              responsive
              customStyles={customStyles}
              paginationPerPage={10}
              paginationRowsPerPageOptions={[10, 25, 50, 100]}
              paginationComponentOptions={{
                rowsPerPageText: 'Rows per page:',
                rangeSeparatorText: 'of',
                noRowsPerPage: false,
              }}
            />
          )}
        </CardBody>
      </StyledCard>

      <AddSubAreaModal
        isOpen={addModalOpen}
        toggle={() => setAddModalOpen(false)}
        areaOptions={areaOptions}
        onSubmit={modaltype == 'add' ? handleAddSubArea : handleEditSubarea}
        loading={addLoading}
        form={form}
        setForm={setForm}
        modaltype={modaltype}
      />
      <ConfirmationModal 
        modal={showconfirmationModal}
        toggle={taggle_confirmation_modal}  
        body={`Are you sure you want to change status of ${confirmation_modal_data?.sub_area_name} sub-area?`}
        header={'Status Change Confirmation'}
        actionbtn={'Update'}
        handleConfirm={handlesubareastatus}
        type={'status change'}
      />
    </Container>
  )
}
