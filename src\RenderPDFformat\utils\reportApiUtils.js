import { errorToast, successToast } from "../../_helper/helper";
import AreaService from "../../api/areaService";

// Helper functions
const getISOWeekNumber = (date) => {
  const tempDate = new Date(date.getTime());
  tempDate.setHours(0, 0, 0, 0);
  tempDate.setDate(tempDate.getDate() + 3 - ((tempDate.getDay() + 6) % 7));
  const week1 = new Date(tempDate.getFullYear(), 0, 4);
  return (
    tempDate.getFullYear() +
    '-W' +
    String(
      1 + Math.round(((tempDate - week1) / 86400000 - 3 + ((week1.getDay() + 6) % 7)) / 7)
    ).padStart(2, '0')
  );
};

const parseWeekToDate = (weekStr) => {
  const [year, week] = weekStr.split('-W');
  const simple = new Date(year, 0, 1 + (week - 1) * 7);
  const dow = simple.getDay();
  const ISOweekStart = simple;
  if (dow <= 4)
    ISOweekStart.setDate(simple.getDate() - simple.getDay() + 1);
  else
    ISOweekStart.setDate(simple.getDate() + 8 - simple.getDay());
  return ISOweekStart;
};

const getCurrentISOWeek = () => {
  const now = new Date();
  return getISOWeekNumber(now);
};

// Function to fetch leaderboard data
export const fetchLeaderboardData = async ({
  factoryId,
  week
}) => {
  try {
    const payload = {
      flag: true,
      factory_id: factoryId,
      week: week || getCurrentISOWeek()
    };

    const response = await AreaService.fetchDataForLeaderBoard(payload);

    if (response.data) {
      const updatedData = response.data.map(item => ({
        ...item,
        time: Math.round(item.time / 60).toFixed(0),
        compliance_percentage: item?.totalalert === 0 ? 100 : item.compliance_percentage
      }));

      return updatedData;
    } else {
      console.log('No data returned from leaderboard fetch operation');
      return [];
    }
  } catch (error) {
    console.error('Error fetching leaderboard data:', error);
    return [];
  }
};

// Main function to fetch records with filters
export const fetchRecordsWithFilter = async ({
  userId,
  factoryId,
  filters = {}
}) => {
  let identifier = '';
  let baseDate = null;
  let periods = [];

  if (!filters.week && !filters.month) {
    filters = {
      ...filters,
      week: getCurrentISOWeek()
    };
  }

  if (filters.week) {
    identifier = 'week';
    baseDate = parseWeekToDate(filters.week);
    for (let i = 0; i < 4; i++) {
      const date = new Date(baseDate);
      date.setDate(baseDate.getDate() - i * 7);
      periods.push(getISOWeekNumber(date));
    }
  } else if (filters.month) {
    identifier = 'month';
    const [year, m] = filters.month.split('-');
    baseDate = new Date(parseInt(year), parseInt(m) - 1, 1);
    for (let i = 0; i < 4; i++) {
      const date = new Date(baseDate);
      date.setMonth(baseDate.getMonth() - i);
      const monthStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      periods.push(monthStr);
    }
  } else {
    console.error("Either week or month must be provided in filters.");
    return [];
  }

  try {
    const responses = [];

    for (const period of periods) {
      const dynamicFilters = {
        approval: "Select Approval",
        module: "",
        severity: "",
        shift: filters.shifts || [],
        date: filters.date || "",
        week: identifier === 'week' ? period : "",
        month: identifier === 'month' ? period : "",
        starting: filters.starting || "",
        ending: filters.ending || "",
        area: filters.area || "",
        subarea: filters.subarea || ""
      };

      const payload = {
        user_id: userId,
        factory_id: factoryId,
        identifier,
        filters: dynamicFilters,
        role: JSON.parse(localStorage.getItem('role')),
        pagination: {
          page_no: 1,
          per_page: 21
        }
      };

      const res = await AreaService.getFilterAlerts(payload);
      responses.push({
        name: period,
        compliance: res.data?.data.accepted_records || 0
      });
    }

    return responses;
  } catch (error) {
    console.error("Error fetching data:", error);
    return [];
  }
};


export const Send_PDF_through_Email = (payload, setemail_loader, setEmail_modal_msg, setShowSuccessMsg) => {
  const formData = new FormData();
  setemail_loader(true);
  setEmail_modal_msg("Sending Pdf please wait...")
  payload.receivers.forEach(email => {
    formData.append("receivers", email);
  });
  if (payload.cc_emails && payload.cc_emails.length > 0) {
    payload.cc_emails.forEach(email => {
      formData.append("cc_emails", email);
    });
  }
  formData.append("subject", payload.subject);
  formData.append("body", payload.body);

  if (payload.attachment) {
    formData.append("attachment", payload.attachment, "AI-Model-Report.pdf");
  }

  AreaService.send_email_with_PDF(formData)
    .then((res) => {
      if (res.status === 200) {
        successToast("PDF successfully sent to email");
      }
      setemail_loader(false);
      setEmail_modal_msg("Email Send")
      setShowSuccessMsg(true)
    })
    .catch((error) => {
      errorToast(error.message || "Error while sending PDF");
      setemail_loader(false)
      setEmail_modal_msg("Error while sending pdf, please try again")
      setShowSuccessMsg(false)
    });
};

export const fetchHeatmapDataForReports = async (userId, factoryId, filters) => {
  console.log('filters123', filters)
  let payload = {
    user_id: userId,
    shift: filters?.shifts?.length > 0 ? filters?.shifts[0] : 'Shift A',
    start_date: filters?.starting || filters.date || '',
    end_date: filters?.ending || filters.date || '',
    weekly: filters?.week,
    month: filters?.month,
    factory_id: factoryId || 0
  }
  try {
    const res = await AreaService.fetchAnalyticsHeatmapGbl(payload)
    return res?.data?.heatmapData;

  } catch (err) {
    console.log("Analytics page, Heatmap chart error", err);

  }
}

export const getSubareasDetailsForPDF = async (userId, factoryId, filters) => {

  try {
    const payload = {
      safety_area: [],
      shift: filters?.shifts?.[0] || 'Shift A',
      start_date:
        filters?.starting !== ""
          ? formatDate(filters?.starting)
          : filters?.date == ""
            ? ""
            : formatDate(filters?.date),
      end_date:
        filters?.ending !== ""
          ? formatDate(filters?.ending)
          : filters?.date == ""
            ? ""
            : formatDate(filters?.date),
      week: filters?.week,
      factory_id: factoryId,
      month: filters?.month,
      user_id: userId
    }
    const res = await AreaService.getAiReportsCards(payload);
    if (res.status == 200) {
      return res
    }
  } catch (error) {
    console.log(error)
  }
}

export const get_shift_based_report = async (userId, factoryId, filters) => {
  // she-net
  try{
      const payload = {
        factory_id : factoryId,
        identifier: filters.week ?  'week' : filters.date ? 'date' : filters.month ? 'month' : filters.starting && 'custom',
        filters: {
          date: filters.date,
          month: filters.month,
          week: filters.week,
          starting: filters.starting,
          ending: filters.ending,
          shift: []
        }
      }

      const res = await AreaService.shift_based(payload)
      if(res.status==200){
     
        return res?.data?.data?.shift_hourly_counts
      }
  }
  catch{

  }
}

export const area_wise_alert_report = async (userId, factoryId, filters) => {
  // she-net
  try{
      const payload = {
        factory_id : `${factoryId}`,
        identifier: filters.week ?  'week' : filters.date ? 'date' : filters.month ? 'month' : filters.starting && 'custom',
        filters: {
          date: filters.date,
          month: filters.month,
          week: filters.week,
          // starting: filters.starting,
          // ending: filters.ending,
          // shift: []
        }
      }

      const res = await AreaService.area_wise_alert_count(payload)
      if(res.status==200){
     
        return res?.data?.data?.breakdown
      }
  }
  catch{
       console.log('Area Wise Api Failed')
  }
}
export const area_shift_wise_alert_report = async (userId, factoryId, filters) => {
  try{
      const payload = {
        factory_id : factoryId,
        identifier: filters.week ?  'week' : filters.date ? 'date' : filters.month ? 'month' : filters.starting && 'custom',
        filters: {
          date: filters.date,
          month: filters.month,
          week: filters.week,
          starting: filters.starting,
          ending: filters.ending,
          shift: []
        }
      }

      const res = await AreaService.shift_based(payload)
      if(res.status==200){
     
        return res?.data?.data?.breakdown
      }
  }
  catch{

  }
}
export const shiftComparison = async (userId, factoryId, filters) => {
  // she-net
  try{
      const payload = {
        factory_id : factoryId,
        identifier: filters.week ?  'week' : filters.date ? 'date' : filters.month ? 'month' : filters.starting && 'custom',
        filters: {
          date: filters.date,
          month: filters.month,
          week: filters.week,
          starting: filters.starting,
          ending: filters.ending,
          shift: []
        }
      }

      const res = await AreaService.shift_comparison(payload)
      if(res.status==200){
     
        return res?.data?.data
      }
  }
  catch{

  }
}
export const shiftAnalysis = async (userId, factoryId, filters) => {
  // she-net
  try{
      const payload = {
        factory_id : factoryId,
        identifier: filters.week ?  'week' : filters.date ? 'date' : filters.month ? 'month' : filters.starting && 'custom',
        filters: {
          date: filters.date,
          month: filters.month,
          week: filters.week,
          starting: filters.starting,
          ending: filters.ending,
          shift: []
        }
      }

      const res = await AreaService.shift_based(payload)
      if(res.status==200){
     
        return res?.data?.data
      }
  }
  catch{

  }
}
   const formatDatee = (dateStr) => {
        const [year, month, day] = dateStr?.split("-");
        return `${month}/${day}/${year}`;
      };
export const moduleAnalysis = async (userId, factoryId, filters) => {
  // she-net
  try{
    const payload = {
        factory_id : factoryId,
        identifier: filters.week ?  'week' : filters.date ? 'date' : filters.month ? 'month' : filters.starting && 'custom',
        filters: {
          date: filters.date,
          month: filters.month,
          week: filters.week,
          starting: filters.starting,
          ending: filters.ending,
          shift: []
        }
      }

      const res = await AreaService.moduleAnalysis(payload)
      if(res.status==200){
     
        return res?.data
      }
  }
  catch{

  }
}
export const complianceAnalysisSubArea = async (userId, factoryId, filters) => {
  // she-net
  try{
      const payload = {
        factory_id : factoryId,
        identifier: filters.week ?  'week' : filters.date ? 'date' : filters.month ? 'month' : filters.starting && 'custom',
        filters: {
          date: filters.date,
          month: filters.month,
          week: filters.week,
          starting: filters.starting,
          ending: filters.ending,
          shift: []
        }
      }

      const res = await AreaService.complianceBySubArea(payload)
      if(res.status==200){
     
        return res?.data
      }
  }
  catch{

  }
}
export const executive_summary_for_report = async (userId, factoryId, filters,defaultShift=false) => {
// she-net
  try {
    const payload = {
      shift: filters?.shifts?.length > 0 ? filters?.shifts[0] : defaultShift ? 'Shift A' : '',
      start_date: filters?.date ? filters?.date : filters.starting,
      end_date: filters?.date ? filters?.date : filters.ending,
      week: filters?.week,
      month: filters?.month,
      factory_id: factoryId || 0,
    }
    const res = await AreaService.executive_summary_for_report(payload);
    if (res.status == 200) {
      return res?.data?.data
    }
  } catch (error) {
    console.log(error)
  }
}
export const get_alerts_for_report = async (userId, factoryId, filters,defaultShift=false) => {
// she-net
  try {
    const payload = {
      shift: filters?.shifts?.length > 0 ? filters?.shifts[0] : defaultShift ? 'Shift A' : '',
      start_date: filters?.date ? filters?.date : filters.starting,
      end_date: filters?.date ? filters?.date : filters.starting,
      week: filters?.week,
      month: filters?.month,
      factory_id: factoryId || 0,
    }
    const res = await AreaService.get_alerts_for_report(payload);
    if (res.status == 200) {
      return res?.data?.data
    }
  } catch (error) {
    console.log(error)
  }
}

export async function fetchAnalyticsBars(userId, factoryId, filters) {
 
  try {
    const payload = {
      area_ids: [],
      user_id: userId,
      factory_id: factoryId,
      areaname: "",
      shift: filters?.shifts?.length > 0 ? filters?.shifts[0] : 'Shift A',
      weekly: filters?.week,
      month: filters?.month
    };

    const res = await AreaService.fetchAnalyticsProgressGbl(payload);
    let totalAlerts;
    let progressData = res?.data?.progressData
    if (res.status == 200) {
      const validBarValues = progressData.filter(
        (item) => item.alerts > 0
      );
       totalAlerts = validBarValues.reduce(
        (sum, item) => sum + item.alerts,
        0
      );

     
    }
    return {totalAlerts, progressData}

  } catch (err) {
    console.log("Analytics page, Progress bars section error", err);

  }
}





const formatDate = (dateStr) => {
  const [year, month, day] = dateStr.split("-");
  return `${month}/${day}/${year}`;
};
export const setDataFormat = (data) => {
  console.log("-------------data format hit----------")
  console.log(data) 
  try {
    const correctFormat = data?.flatMap((item, key) => {
      const groupedSubareas = item?.SubAreas?.reduce((acc, subarea) => {
        if (!acc[subarea?.SubAreaName]) {
          acc[subarea?.SubAreaName] = {
            totalCompliance: 0,
            totalViolations: 0,
            subareaName: subarea?.SubAreaName,
            violations: subarea?.Violations,
            cameraCount: 0,
            Modules: subarea?.Modules
          };
        }
        acc[subarea?.SubAreaName].totalCompliance += subarea?.Compliance;
        acc[subarea?.SubAreaName].totalViolations += subarea?.Violations;
        acc[subarea?.SubAreaName].cameraCount += 1;
        return acc;
      }, {});

      return Object?.values(groupedSubareas)?.map((subarea) => {
        const total = subarea?.totalCompliance + subarea?.totalViolations;
        const checkDecimal = total ? ((subarea?.totalCompliance / total) * 100) : 0
        // const calculated = total ? ((subarea.totalCompliance / total) * 100).toFixed(1) : 0;
        const calculated = Number?.isInteger(checkDecimal) ? checkDecimal?.toFixed(0) : checkDecimal?.toFixed(1);
        const violationss = subarea?.totalViolations
        return {
          areaName: item?.AreaName,
          areaOwner: item?.AreaOwner,
          totalCameras: subarea?.cameraCount,
          subareaName: subarea?.subareaName,
          alerts: violationss,
          calculated,
          compliance: calculated > 0 ? calculated : subarea?.violations == 0 ? 100 : 0,
          Modules: subarea?.Modules
        };
      });
    });
    return correctFormat
  } catch (error) {
    errorToast('Error found in data format');
    console.log(error)
  }
}
