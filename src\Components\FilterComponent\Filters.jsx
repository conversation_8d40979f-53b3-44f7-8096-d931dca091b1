import React, { useState, useEffect, useRef } from 'react';
import { Button, Input } from 'reactstrap';
import { IoCheckmarkOutline } from 'react-icons/io5';
import { RxReset } from 'react-icons/rx';
import { Filter } from 'react-feather';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getCurrentWeekWithYear } from '../../utils/currentWeekWithYear';

function FilterComponent({ onFilterChange, SelectedFilters, setSelectedFilters }) {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [filterTypeOpen, setFilterTypeOpen] = useState(false);
  const [filterType, setFilterType] = useState('week');
  const [filterValue, setFilterValue] = useState('');
  const [accepted, setAccepted] = useState(false);

  const wrapperRef = useRef(null);
  const currentWeekk = getCurrentWeekWithYear();

  function getCurrentWeek() {
    const now = new Date();
    const onejan = new Date(now.getFullYear(), 0, 1);
    const dayOfYear = ((now - onejan + 86400000) / 86400000);
    const week = Math.ceil(dayOfYear / 7);
    return `${now.getFullYear()}-W${week.toString().padStart(2, '0')}`;
  }

  useEffect(() => {
    setFilterValue(currentWeekk);
    setSelectedFilters({
      identifier: 'week',
      period: currentWeekk,
    });
  }, []);

  useEffect(() => {
    function handleClickOutside(event) {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {
        setDropdownOpen(false);
        setFilterTypeOpen(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const toggleDropdown = () => {
    setDropdownOpen(prev => !prev);
    setFilterTypeOpen(false);
  };

  const toggleFilterType = () => {
    setFilterTypeOpen(prev => !prev);
  };

  const handleFilterTypeSelect = (type) => {
    setFilterType(type);
    setAccepted(false);
    setFilterTypeOpen(false);
    // // Set filterValue according to type
    // if (type === 'week') setFilterValue(currentWeekk);
    // else {
    //   const now = new Date();
    //   setFilterValue(now.toISOString().slice(0, 7)); // YYYY-MM format for month
    // }
  };

  const handleValueChange = (e) => {
    setFilterValue(e.target.value);
    setAccepted(false);
  };

  const handleAccept = () => {
    if (!filterValue) {
      toast.error("Please select a value before accepting.");
      return;
    }
    setAccepted(true);
    setSelectedFilters({ identifier: filterType, period: filterValue });
    if (onFilterChange) onFilterChange({ identifier: filterType, period: filterValue });
  };

  const handleReset = () => {
    setFilterType('week');
    setFilterValue(currentWeekk);
    setAccepted(false);
    setSelectedFilters({ identifier: 'week', period: currentWeekk });
    if (onFilterChange) onFilterChange({ identifier: 'week', period: currentWeekk });
  };

  const style = {
    minWidth: "182px",
    width: "182px",
    maxWidth: "182px",
    height: "38px",
    fontSize: 13,
  };

  return (
   
      <div ref={wrapperRef} style={{ position: 'relative'}}>
        <div
          type="button"
          className={`d-flex justify-content-center  filter-btnn`}
          onClick={toggleDropdown}
          style={{ cursor: 'pointer' }}
        >
          <p className="m-0" style={{ fontSize: "16px" }}>Filters</p>
          <span className="d-flex">
            <Filter color="#fff" size={16} className="ms-2" />
          </span>
        </div>
        {dropdownOpen && (
          <div style={{
            position: 'absolute',
            top: '40px',
            left: -70,
            border: '1px solid #ddd',
            borderRadius: '4px',
            background: 'white',
            boxShadow: '0 2px 6px rgba(0,0,0,0.2)',
            padding: '10px',
            width: '220px',
            zIndex: 2,
          }}>
            {/* Filter Type Dropdown */}
            <div style={{ marginBottom: '10px', position: 'relative' }}>
              <div
                onClick={toggleFilterType}
                style={{
                  cursor: 'pointer',
                  border: '1px solid #ccc',
                  padding: '8px 10px',
                  borderRadius: '4px',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <span>{filterType.charAt(0).toUpperCase() + filterType.slice(1)}</span>
                <span style={{ fontWeight: 'bold' }}>{filterTypeOpen ? '▲' : '▼'}</span>
              </div>
              {filterTypeOpen && (
                <div style={{
                  position: 'absolute',
                  top: '40px',
                  left: 0,
                  border: '1px solid #ccc',
                  borderRadius: '4px',
                  background: 'white',
                  width: '100%',
                  zIndex: 1001,
                }}>
                  <div
                    onClick={() => handleFilterTypeSelect('week')}
                    style={{ padding: '8px 10px', cursor: 'pointer', borderBottom: '1px solid #eee' }}
                  >
                    Week
                  </div>
                  <div
                    onClick={() => handleFilterTypeSelect('month')}
                    style={{ padding: '8px 10px', cursor: 'pointer' }}
                  >
                    Month
                  </div>
                </div>
              )}
            </div>

            {/* Input field based on filterType */}
            <div style={{ marginBottom: '10px' }}>
              {filterType === 'week' && (
                <Input
                  type="week"
                  value={filterValue}
                  onChange={handleValueChange}
                  max={currentWeekk}
                />
              )}
              {filterType === 'month' && (
                <Input
                  type="month"
                  value={filterValue}
                  onChange={handleValueChange}
                />
              )}
            </div>

            {/* Buttons */}
            <div className='d-flex justify-content-center flex-column align-items-center'>
              {SelectedFilters.period === getCurrentWeek() && filterValue === currentWeekk ? null : (
                <>
                  <Button
                    style={{ ...style, justifySelf: 'center' }}
                    className={`mx-2 p-0 rounded-3 shadow-sm d-flex align-items-center justify-content-evenly`}
                    onClick={handleAccept}
                    color=""
                  >
                    <IoCheckmarkOutline
                      style={{
                        color: "#22c65e",
                        fontSize: "20px",
                        transform: "rotate(20deg)",
                      }}
                    />
                    <p style={{ color: "#22c65e" }} className="m-0 p-0 "> Accept </p>
                  </Button>
                  <Button
                    style={{ ...style, justifySelf: 'center' }}
                    className={`mx-2 mt-3 rounded-3 shadow-sm d-flex align-items-center justify-content-evenly box-shadow`}
                    onClick={handleReset}
                    color=""
                  >
                    <RxReset
                      style={{
                        color: "#4e74d4",
                        fontSize: "20px",
                      }}
                    />
                    <p style={{ color: "#4e74d4" }} className="m-0 p-0 "> Reset </p>
                  </Button>
                </>
              )}
            </div>
          </div>
        )}

      <ToastContainer position="top-right" autoClose={3000} />
      </div>
  
  );
}

export default FilterComponent;
