import React, { Fragment, useContext, useEffect, useState } from "react";
import { Card, Container, Row, Col, CardBody } from "react-bootstrap";
import Loader3 from "../../../../CommonElements/Spinner/loader3";
import AreaService from "../../../../api/areaService";
import CameraService from "../../../../api/cameraService";
import { errorToast } from "../../../../_helper/helper";
import HeatmapForArea from "../../../Screens/GlobalUser/LiveAnalytics/New componens/Grapgh/HeatmapForArea";
import NewDonutChart from '../../../Screens/GlobalUser/LiveAnalytics/New componens/Grapgh/NewDonutChart';
import NewBarChart from "../../../Screens/GlobalUser/LiveAnalytics/New componens/Grapgh/NewBarChart";
import AlertTrendChartNew from "../../../Screens/GlobalUser/LiveAnalytics/components/AlertTrendChartNew";
import LiveAnalyticsContext from '../../../../_helper/formData/LiveAnalytics/LiveAnalytics';

const FactoryCharts = ({ id, locationData, filters, NoEvent }) => {
    // Contexts
    const {
        setOverAllComplaince,
        setCameraCountContext,
        setAiAccuracyContext,
        setHighestAlerts,
        setProgressContext,
        setheatdatacontext,
        alerttrendcontext,
        heatmapcontext,
        setdashfiltercontext,
    } = useContext(LiveAnalyticsContext);

    // State
    const [accuracyPercent, setAccuracyPercent] = useState();
    const [recentOrderChart, setRecentOrderChart] = useState({ series: [0] });
    const [progressData, setProgressData] = useState();
    const [week, setWeek] = useState();
    const [loadingForBars, setLoadingForBars] = useState(true);
    const [loadingForDonut, setLoadingForDonut] = useState(true);
    const [loaderforHeatmap, setLoaderForHeatmap] = useState(true);
    const [heatmapData, setHeatmapData] = useState({});
    const [chartData, setChartData] = useState({ categories: [], series: [] });

    // FactoryID
    const factoryID = React.useMemo(() => {
        try {
            return JSON.parse(localStorage.getItem('userData'))?.factory?.id || 0;
        } catch {
            return 0;
        }
    }, []);

    // Area Info (helper)
    const getAreaInfo = () => {
        if (id) {
            const areaPayload = JSON.parse(localStorage.getItem('areaPayload'));
            return {
                areaName: areaPayload?.name,
                areaID: areaPayload?.id,
                ownerId: areaPayload?.area_owner_id
            };
        } else {
            return {
                areaName: locationData?.name,
                areaID: locationData?.id,
                ownerId: locationData?.ownerId
            };
        }
    };

    // AI Accuracy
    useEffect(() => {
        const { areaID, ownerId, areaName } = getAreaInfo();
        console.log('areadadfadfa', areaID, ownerId)
        if (!filters) return;
        console.log('filterss2', filters)

        async function fetchAI() {
            try {
                const payload = {
                    user_id: ownerId,
                    factory_id: factoryID,
                    identifier: filters.week ? "week" : (filters?.starting || filters?.ending) ? "custom" : filters.date ? "date" : "month",
                    role: JSON.parse(localStorage.getItem('role')),
                    filters: {
                        approval: "Select Approval",
                        module: "",
                        severity: "",
                        shift: [filters.shift],
                        date: filters.date,
                        week: filters.week,
                        month: filters.month,
                        starting: filters?.starting,
                        ending: filters?.ending,
                        area: id ? id : areaName,
                        subarea: ""
                    },
                    pagination: { page_no: 1, per_page: 21 }
                };
                const res = await AreaService.getFilterAlerts(payload);
                if (res?.status === 200 || res?.statusText?.toLowerCase() === "ok") {
                    const accepted = res?.data?.data?.accepted_records || 0;
                    const rejected = res?.data?.data?.rejected_records || 0;
                    const percent = (accepted + rejected) ? (accepted / (accepted + rejected)) * 100 : 0;
                    setAccuracyPercent(Math.round(percent));
                    setAiAccuracyContext && setAiAccuracyContext(Math.round(percent));
                }
            } catch (e) {
                setAccuracyPercent(undefined);
            }
        }
        fetchAI();
    }, [filters, id, locationData, factoryID, setAiAccuracyContext]);

    // Progress Bar of Area
    useEffect(() => {
        const { areaName, areaID, ownerId } = getAreaInfo();
        if (!filters) return;
        async function fetchProgress() {
            setLoadingForBars(true);
            setLoadingForDonut(true);
            try {
                const { week, ...restFilters } = filters
                const payload = {
                    ...restFilters,
                    areaname: areaName,
                    area_ids: [areaID],
                    user_id: ownerId,
                    factory_id: factoryID,
                    weekly: week
                };

                const res = await AreaService.progressBarOFArea(payload);
                if (res.status === 200) {
                    const validBarValues = (res?.data?.progressData || []).filter(item => item.barValue > 0);
                    const totalBarValue = validBarValues.reduce((sum, item) => sum + item.barValue, 0);
                    const averageBarValue = validBarValues.length > 0 ? totalBarValue / validBarValues.length : 0;
                    setRecentOrderChart((prev) => ({
                        ...prev,
                        series: [Math.round(averageBarValue).toFixed(0)],
                    }));
                    setOverAllComplaince && setOverAllComplaince(Math.round(averageBarValue).toFixed(0));
                    setProgressData(res?.data?.progressData || []);
                    setProgressContext && setProgressContext(res?.data?.progressData || []);
                }
                setLoadingForBars(false);
                setLoadingForDonut(false);
            } catch (error) {
                setLoadingForBars(false);
                setLoadingForDonut(false);
                errorToast('Error while fetching progress bar data');
            }
        }
        fetchProgress();
    }, [filters, id, locationData, factoryID, setOverAllComplaince, setProgressContext]);

    // Heatmap
    useEffect(() => {
        const { areaName, areaID, ownerId } = getAreaInfo();
        if (!filters) return;
        async function fetchHeatmap() {
            setLoaderForHeatmap(true);
            try {
                const payload = {
                    factory_id: factoryID,
                    user_id: ownerId,
                    shift: filters.shift,
                    weekly: filters.week,
                    month: filters.month,
                    areaname: areaName,
                    area_ids: areaID,
                    start_date: filters?.starting,
                    end_date: filters?.ending,
                    date:filters.date 
                };
                setdashfiltercontext && setdashfiltercontext({
                    month: filters.month,
                    weekly: filters.week,
                    shift: filters.shift
                });
                const res = await AreaService.get_live_heatmap_by_subarea(payload);
                setHeatmapData(res?.data?.heatmapData || {});
                setheatdatacontext && setheatdatacontext(res?.data?.heatmapData || {});
                setLoaderForHeatmap(false);
            } catch (error) {
                setLoaderForHeatmap(false);
                errorToast('Error while fetching heatmap data');
            }
        }
        fetchHeatmap();
    }, [filters, id, locationData, factoryID, setdashfiltercontext, setheatdatacontext]);

    // Camera count: only if you need it in this component
    useEffect(() => {
        const { areaID } = getAreaInfo();
        async function fetchCamera() {
            try {
                const res = await CameraService.getCameraCountsArea(areaID);
                setCameraCountContext && setCameraCountContext(res?.data?.data || {});
            } catch (e) { /* Ignore errors */ }
        }
        fetchCamera();
    }, [filters, id, locationData, setCameraCountContext]);

    // Render
    return (
        <Fragment>
            <Container fluid={true}>
                <Row>
                    <Col xs='12' ref={heatmapcontext}>
                        <HeatmapForArea
                            heatmapData={{
                                ...(heatmapData || {}),
                                data: Array.isArray(heatmapData?.data) ? heatmapData.data : [],
                            }}
                            moduleLength={Array.isArray(progressData) ? progressData.length : 0}
                            loader={loaderforHeatmap}
                            filters={filters}
                            AreaDashboard={true}
                            NoEvent={NoEvent}
                        />

                    </Col>
                    <Col xl='12' xxl='6' >
                        <Card style={{ minHeight: '399px', borderRadius: '32px' }}>
                            <h5 className="px-4 py-3" style={{ fontSize: '24px', fontWeight: '500', color: '#383838', }}>Overall Compliance Score</h5>
                            {loadingForDonut ? (
                                <span className="w-100 h-100 d-flex justify-content-center align-items-center position-absolute"><Loader3 /></span>
                            ) : (
                                <CardBody className="position-relative p-0">
                                    <Row>
                                        <Col md='5' className="d-flex justify-content-center align-items-center  pe-0" style={{ height: '320px' }}>
                                            <NewDonutChart
                                                seriess={Array.isArray(recentOrderChart.series) ? recentOrderChart.series : []}
                                                height={240}
                                                tooltip={Array.isArray(progressData) ? progressData : []}
                                            />
                                        </Col>
                                        <Col md='7' className="ps-4 ps-ms-auto">
                                            <NewBarChart
                                                progressData={Array.isArray(progressData) ? progressData : []}
                                                week={week}
                                                loadingForBars={loadingForBars}
                                                areaFlag={true}
                                            />
                                        </Col>
                                    </Row>
                                </CardBody>
                            )}
                        </Card>
                    </Col>
                    <Col md={12} xxl='6'>
                        <AlertTrendChartNew
                            ref={alerttrendcontext}
                            chartData={{
                                categories: Array.isArray(chartData.categories) ? chartData.categories : [],
                                series: Array.isArray(chartData.series) ? chartData.series : [],
                            }}
                            area_ID={id || locationData?.name}
                            filters={filters}
                        />
                    </Col>
                </Row>
            </Container>
        </Fragment>
    );
};

export default FactoryCharts;
