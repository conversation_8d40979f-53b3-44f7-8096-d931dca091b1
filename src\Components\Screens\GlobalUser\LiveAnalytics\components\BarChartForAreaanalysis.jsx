// import React, { useState, useEffect } from 'react';
// import { Card, CardBody, CardHeader, Row, Col, FormGroup, Input } from 'reactstrap';
// import Chart from 'react-apexcharts';
// import AreaService from '../../../../../api/areaService';

// const BarChartForAreaanalysis = ({ chartData, callApi, filters, subAreaPage }) => {
//     // State for selected area filter
//     const [selectedArea, setSelectedArea] = useState('');
//     const [selectedSubArea, setSelectedSubArea] = useState('');
//     const [isLoading, setIsLoading] = useState(true);
//     const [areas, setAreas] = useState([]);

//     // State for sub-areas from API
//     const [subAreas, setSubAreas] = useState([]);
//     // State for module analytics data
//     const [moduleAnalytics, setModuleAnalytics] = useState({
//         "Total Alerts": 0,
//         "modules": [],
//         "data": []
//     });
//     // Factory ID from local storage
//     const factoryId = JSON.parse(localStorage.getItem('userData'))?.factory?.id || 14;
//     // User ID from local storage
//     const userId = JSON.parse(localStorage.getItem('userData'))?.id || 89;

//     // Fetch all areas on component mount
//     useEffect(() => {
//         fetchAllAreas();
//     }, []);

//     // Fetch module analytics when selected area or sub-area changes
//     useEffect(() => {
//         if (selectedArea) {
//             if (subAreaPage) {
//                 // For sub-area page, pass both area and sub-area
//                 fetchModuleAnalytics(selectedArea, selectedSubArea);
//             } else {
//                 // For area page, just pass the area
//                 fetchModuleAnalytics(selectedArea);
//             }
//         }
//     }, [selectedArea, selectedSubArea, callApi, subAreaPage]);

//     // Function to fetch all areas and extract all sub-areas
//     const fetchAllAreas = async () => {
//         setIsLoading(true);
//         try {
//             const res = await AreaService.GetAllAreas(factoryId);
//             console.log('Areas data:', res?.data);

//             if (res?.data?.success && res?.data?.data?.areas) {
//                 // Filter only active areas
//                 const activeAreas = res.data.data.areas.filter(area => area.active);
//                 setAreas(activeAreas);

//                 // Set the first active area as default selected area
//                 if (activeAreas.length > 0) {
//                     setSelectedArea(activeAreas[0].id);
//                 }

//                 // If subAreaPage is true, collect all sub-areas from all active areas
//                 if (subAreaPage) {
//                     // Extract all sub-areas from active areas
//                     const allSubAreas = [];

//                     activeAreas.forEach(area => {
//                         if (area.sub_area && area.sub_area.length > 0) {
//                             area.sub_area.forEach(subArea => {
//                                 // Check if this sub-area is already in the array (avoid duplicates)
//                                 if (!allSubAreas.some(sa => sa.id === subArea.id)) {
//                                     allSubAreas.push(subArea);
//                                 }
//                             });
//                         }
//                     });

//                     // Sort sub-areas by name for better user experience
//                     allSubAreas.sort((a, b) => a.name.localeCompare(b.name));

//                     // Add "All" option at the beginning with empty string value
//                     const subAreasWithAll = [
//                         { id: "", name: "All" },
//                         ...allSubAreas
//                     ];

//                     setSubAreas(subAreasWithAll);

//                     // Set default selected sub-area to "All" (empty string)
//                     setSelectedSubArea("");
//                 }
//             }
//         } catch (error) {
//             console.error('Error fetching areas:', error);
//         } finally {
//             setIsLoading(false);
//         }
//     };

//     // Function to fetch module analytics by area or sub-area
//     const fetchModuleAnalytics = async (areaCode, subAreaCode = null) => {
//         setIsLoading(true);
//         try {
//             // Base payload
//             const payload = {
//                 weekly: filters?.week,
//                 month: filters?.month,
//                 shift: filters?.shifts,
//                 factory_id: factoryId,
//                 user_id: userId
//             };

//             // Add area or sub-area parameter based on the page type and selection
//             if (subAreaPage) {
//                 if (subAreaCode && subAreaCode !== "") {
//                     // If a specific sub-area is selected (not "All")
//                     payload.sub_area_id = parseInt(subAreaCode);
//                     console.log(`Using sub_area_id: ${subAreaCode} for sub-area analytics`);
//                 } else {
//                     // If "All" is selected (empty string) or no sub-area is selected
//                     // We still need to include safety_area for the API
//                     payload.safety_area_id = parseInt(areaCode);
//                     console.log(`Using safety_area: ${areaCode} for all sub-areas`);
//                 }
//             } else {
//                 // For area page, always use safety_area
//                 payload.safety_area_id = parseInt(areaCode);
//             }

//             console.log('Fetching module analytics with payload:', payload);
//             let res;

//             if (subAreaPage) {
//                 res = await AreaService.module_analytics_by_sub_area(payload);
//             } else {
//                 res = await AreaService.module_analytics_by_area(payload);
//             }

//             console.log('Module analytics data:', res?.data);

//             if (res?.data) {
//                 setModuleAnalytics(res.data);
//             }
//         } catch (error) {
//             console.error('Error fetching module analytics:', error);
//         } finally {
//             setIsLoading(false);
//         }
//     };

//     // We'll use the moduleAnalytics data from the API instead of dummy data

//     // State for chart options and series
//     const [chartOptions, setChartOptions] = useState({
//         chart: {
//             type: 'bar',
//             stacked: true,
//             toolbar: {
//                 show: false
//             }
//         },
//         plotOptions: {
//             bar: {
//                 horizontal: false,
//                 columnWidth: '55%',
//                 endingShape: 'rounded',
//                 borderRadius: 8,
//                 borderRadiusApplication: 'end',
//                 borderRadiusWhenStacked: 'last'
//             },
//         },
//         dataLabels: {
//             enabled: true,
//             formatter: function (val) {
//                 return val > 0 ? val : '';
//             },
//             style: {
//                 fontSize: '12px',
//                 colors: ['#fff']
//             }
//         },
//         xaxis: {
//             categories: [],

//         },
//         yaxis: {
//             title: {
//                 text: 'Alerts'
//             },
//             min: 0,
//             forceNiceScale: true,
//         },
//         colors: ['#FF4560', '#F9A825', '#2E93fA'],
//         legend: {
//             position: 'bottom',
//             horizontalAlign: 'center',
//             offsetY: 0
//         },
//         grid: {
//             borderColor: '#e7e7e7',
//             row: {
//                 colors: ['#f3f3f3', 'transparent'],
//                 opacity: 0.5
//             },
//         },
//         tooltip: {
//             y: {
//                 formatter: function (val) {
//                     return val;
//                 }
//             }
//         }
//     });

//     const [chartSeries, setChartSeries] = useState([
//         {
//             name: 'Alerts',
//             data: []
//         }
//     ]);

//     // Calculate total alerts from module analytics data
//     const calculateTotals = () => {
//         // Get total alerts from moduleAnalytics
//         const total = moduleAnalytics["Total Alerts"] || 0;

//         // Calculate a dummy AI accuracy (just for demonstration)
//         // In a real application, this would come from the API
//         const accuracy = Math.floor(Math.random() * 30) + 70; // Random between 70-100%

//         return { total, accuracy };
//     };

//     // Update chart data when moduleAnalytics changes
//     // useEffect(() => {
//     //     try {
//     //         if (!moduleAnalytics || !moduleAnalytics.modules || !moduleAnalytics.data) {
//     //             console.log('No module analytics data available yet');
//     //             return;
//     //         }

//     //         // Get modules and data from moduleAnalytics
//     //         const modules = moduleAnalytics.modules || [];
//     //         const data = moduleAnalytics.data || [];

//     //         // Create series data
//     //         const series = [
//     //             {
//     //                 name: 'Alerts',
//     //                 data: data
//     //             }
//     //         ];

//     //         // Update series data and chart options
//     //         setChartSeries(series);
//     //         setChartOptions(prevOptions => ({
//     //             ...prevOptions,
//     //             xaxis: {
//     //                 ...prevOptions.xaxis,
//     //                 categories: modules
//     //             }
//     //         }));

//     //     } catch (error) {
//     //         console.error('Error updating chart data:', error);
//     //     }
//     // }, [moduleAnalytics]);
//     // ... baki imports aur code waise ke waise ...

// // Yeh function aapke component ke andar hoga
// useEffect(() => {
//   try {
//     if (!moduleAnalytics || !moduleAnalytics.modules || !moduleAnalytics.data) {
//       console.log('No module analytics data available yet');
//       return;
//     }

//     const modules = moduleAnalytics.modules || [];
//     const data = moduleAnalytics.data || [];

//     // Severity groups
//     const severityGroups = {
//       'High Severity': ['MMHE', 'Emergency Exit'],
//       'Medium Plus Severity': ['Helmet', 'Vest'],
//       'Medium Severity': ['Machine Guard'],
//     };

//     // Color mapping for severity groups
//     const groupColors = {
//       'High Severity': '#ef4343',      // red
//       'Medium Plus Severity': '#f0ab43', // orange
//       'Medium Severity': '#40a1ff',    // blue
//     };

//     // Helper: find which group a category belongs to
//     const getSeverityGroup = (category) => {
//       for (const group in severityGroups) {
//         if (severityGroups[group].includes(category)) return group;
//       }
//       return null; // ignore other categories or handle as you like
//     };

//     // Initialize groups with empty arrays
//     const grouped = {
//       'High Severity': [],
//       'Medium Plus Severity': [],
//       'Medium Severity': []
//     };

//     // Populate groups if module exists in API response
//     modules.forEach((mod, idx) => {
//       const group = getSeverityGroup(mod);
//       if (group) {
//         grouped[group].push({ category: mod, value: data[idx] });
//       }
//     });

//     // Sort each group by descending alerts (value)
//     Object.keys(grouped).forEach(group => {
//       grouped[group].sort((a, b) => b.value - a.value);
//     });

//     // Build series array for apexcharts with colors
//     const series = [
//       {
//         name: 'High Severity',
//         data: grouped['High Severity'].map(item => item.value),
//         color: groupColors['High Severity'],
//       },
//       {
//         name: 'Medium Plus Severity',
//         data: grouped['Medium Plus Severity'].map(item => item.value),
//         color: groupColors['Medium Plus Severity'],
//       },
//       {
//         name: 'Medium Severity',
//         data: grouped['Medium Severity'].map(item => item.value),
//         color: groupColors['Medium Severity'],
//       }
//     ];

//     // Build categories concatenated in the order: High, Medium Plus, Medium
//     const categories = [
//       ...grouped['High Severity'].map(item => item.category),
//       ...grouped['Medium Plus Severity'].map(item => item.category),
//       ...grouped['Medium Severity'].map(item => item.category)
//     ];

//     // Update state
//     setChartSeries(series);
//     setChartOptions(prev => ({
//       ...prev,
//       xaxis: {
//         ...prev.xaxis,
//         categories,
//       },
//       colors: [
//         groupColors['High Severity'],
//         groupColors['Medium Plus Severity'],
//         groupColors['Medium Severity']
//       ],
//       legend: {
//         position: 'bottom',
//         horizontalAlign: 'center',
//         offsetY: 0,
//         formatter: (seriesName) => seriesName
//       }
//     }));

//   } catch (error) {
//     console.error('Error updating chart data:', error);
//   }
// }, [moduleAnalytics]);


//     // Handle area selection change
//     const handleAreaChange = (e) => {
//         const newAreaId = e.target.value;
//         setSelectedArea(newAreaId);
//     };

//     // Handle sub-area selection change
//     const handleSubAreaChange = (e) => {
//         const newSubAreaId = e.target.value;
//         setSelectedSubArea(newSubAreaId);
//     };

//     // Calculate totals from moduleAnalytics
//     const { total = 0, accuracy = 0 } = calculateTotals();

//     return (
//         <Card className="mb-4" style={{ borderRadius: '32px', }}>
//             <CardHeader className="pb-0" style={{ background: '#f8f9fa', borderBottom: 'none', borderTopLeftRadius: '32px', borderTopRightRadius: '32px' }}>
//                 <Row className="align-items-center">
//                     <Col>
//                         <h6 className="mb-0" style={{ color: '#333', fontWeight: '600' }}>
//                             Module Analytics
//                         </h6>
//                         <div className="mt-2">
//                             {/* <span style={{ color: '#666', marginRight: '20px', fontSize: '12px' }}>
//                                 Total Alerts: <span style={{ fontWeight: '600', color: '#333' }}>{total}</span>
//                             </span> */}
//                             <span style={{ color: '#666', fontSize: '12px' }}>
//                                 {/* AI Accuracy: <span style={{ fontWeight: '600', color: '#333' }}>{accuracy}%</span> */}
//                             </span>
//                         </div>
//                     </Col>
//                     <Col xs="auto">
//                         <FormGroup className="mb-0 d-flex">
//                             {!subAreaPage && (
//                                 <Input
//                                     type="select"
//                                     name="areaSelect"
//                                     id="areaSelect"
//                                     value={selectedArea}
//                                     onChange={handleAreaChange}
//                                     style={{
//                                         borderRadius: '4px',
//                                         border: '1px solid #ced4da',
//                                         padding: '8px 12px',
//                                         minWidth: '150px',
//                                         fontSize: '14px'
//                                     }}
//                                 >
//                                     {areas.map(area => (
//                                         <option style={{ fontSize: '14px' }} key={area.id} value={area.id}>
//                                             {area.area} - {area.areaOwner || 'No Owner'}
//                                         </option>
//                                     ))}
//                                 </Input>
//                             )}

//                             {subAreaPage && (
//                                 <Input
//                                     type="select"
//                                     name="subAreaSelect"
//                                     id="subAreaSelect"
//                                     value={selectedSubArea}
//                                     onChange={handleSubAreaChange}
//                                     style={{
//                                         borderRadius: '4px',
//                                         border: '1px solid #ced4da',
//                                         padding: '8px 12px',
//                                         minWidth: '200px',
//                                         fontSize: '14px'
//                                     }}
//                                 >
//                                     {subAreas.map(subArea => (
//                                         <option style={{ fontSize: '14px' }} key={subArea.id || 'all'} value={subArea.id}>
//                                             {subArea.name}
//                                         </option>
//                                     ))}
//                                 </Input>
//                             )}
//                         </FormGroup>
//                     </Col>
//                 </Row>
//             </CardHeader>
//             <CardBody>
//                 <div className="chart-container" style={{ marginTop: '20px', padding: '0 10px', position: 'relative' }}>
//                     {isLoading && (
//                         <div style={{
//                             position: 'absolute',
//                             top: 0,
//                             left: 0,
//                             right: 0,
//                             bottom: 0,
//                             display: 'flex',
//                             justifyContent: 'center',
//                             alignItems: 'center',
//                             backgroundColor: 'rgba(255, 255, 255, 0.7)',
//                             zIndex: 10
//                         }}>
//                             <div className="spinner-border text-primary" role="status">
//                                 <span className="sr-only">Loading...</span>
//                             </div>
//                         </div>
//                     )}
//                     {!isLoading && moduleAnalytics.modules && moduleAnalytics.modules.length === 0 ? (
//                         <div style={{
//                             display: 'flex',
//                             justifyContent: 'center',
//                             alignItems: 'center',
//                             height: '265px',
//                             color: '#666'
//                         }}>
//                             No data available for this area
//                         </div>
//                     ) : (
//                         <Chart
//                             options={chartOptions}
//                             series={chartSeries}
//                             type="bar"
//                             height={265}
//                         />
//                     )}
//                 </div>
//             </CardBody>
//         </Card>
//     );
// };

// export default BarChartForAreaanalysis;












































// import React, { useState, useEffect } from 'react';
// import { Card, CardBody, CardHeader, Row, Col, FormGroup, Input } from 'reactstrap';
// import Chart from 'react-apexcharts';
// import AreaService from '../../../../../api/areaService';

// const BarChartForAreaanalysis = ({ chartData, callApi, filters, subAreaPage }) => {
//     // State for selected area filter
//     const [selectedArea, setSelectedArea] = useState('');
//     const [selectedSubArea, setSelectedSubArea] = useState('');
//     const [isLoading, setIsLoading] = useState(true);
//     const [areas, setAreas] = useState([]);
//     const [subAreas, setSubAreas] = useState([]);
//     const [moduleAnalytics, setModuleAnalytics] = useState({
//         totalAlerts: 0,
//         modules: [],
//         data: []
//     });

//     const factoryId = JSON.parse(localStorage.getItem('userData'))?.factory?.id || 14;
//     const userId = JSON.parse(localStorage.getItem('userData'))?.id || 89;

//     // Fetch all areas on component mount
//     useEffect(() => {
//         fetchAllAreas();
//     }, []);

//     // Fetch module analytics when selected area or sub-area changes
//     useEffect(() => {
//         if (selectedArea) {
//             if (subAreaPage) {
//                 fetchModuleAnalytics(selectedArea, selectedSubArea);
//             } else {
//                 fetchModuleAnalytics(selectedArea);
//             }
//         }
//     }, [selectedArea, selectedSubArea, callApi, subAreaPage]);

//     // Fetch all areas and sub-areas
//     const fetchAllAreas = async () => {
//         setIsLoading(true);
//         try {
//             const res = await AreaService.GetAllAreas(factoryId);
//             if (res?.data?.success && res?.data?.data?.areas) {
//                 const activeAreas = res.data.data.areas.filter(area => area.active);
//                 setAreas(activeAreas);
//                 if (activeAreas?.length > 0) setSelectedArea(activeAreas[0].id);

//                 if (subAreaPage) {
//                     const allSubAreas = [];
//                     activeAreas.forEach(area => {
//                         if (area.sub_area && area?.sub_area?.length > 0) {
//                             area.sub_area.forEach(subArea => {
//                                 if (!allSubAreas.some(sa => sa.id === subArea.id)) allSubAreas.push(subArea);
//                             });
//                         }
//                     });
//                     allSubAreas.sort((a, b) => a.name.localeCompare(b.name));
//                     setSubAreas([{ id: "", name: "All" }, ...allSubAreas]);
//                     setSelectedSubArea("");
//                 }
//             }
//         } catch (error) {
//             console.error('Error fetching areas:', error);
//         } finally {
//             setIsLoading(false);
//         }
//     };

//     // Fetch module analytics by area or sub-area
//     const fetchModuleAnalytics = async (areaCode, subAreaCode = null) => {
//         setIsLoading(true);
//         try {
//             const payload = {
//                 weekly: filters?.week,
//                 month: filters?.month,
//                 shift: filters?.shifts,
//                 factory_id: factoryId,
//                 user_id: userId
//             };

//             if (subAreaPage) {
//                 if (subAreaCode && subAreaCode !== "") {
//                     payload.sub_area_id = parseInt(subAreaCode);
//                 } else {
//                     payload.safety_area_id = parseInt(areaCode);
//                 }
//             } else {
//                 payload.safety_area_id = parseInt(areaCode);
//             }

//             let res;
//             if (subAreaPage) {
//                 res = await AreaService.module_analytics_by_sub_area(payload);
//             } else {
//                 res = await AreaService.module_analytics_by_area(payload);
//             }

//             if (res?.data) {
//                 console.log('ress?.dataaa', res?.data)
//                 setModuleAnalytics({
//                     totalAlerts: res.data["Total Alerts"] ?? 0,
//                     modules: Array.isArray(res?.data?.modules) ? res?.data?.modules : [],
//                     data: Array.isArray(res?.data?.data) ? res?.data?.data : []
//                 })
//             }
//         } catch (error) {
//             console.error('Error fetching module analytics:', error);
//         } finally {
//             setIsLoading(false);
//         }
//     };

//     // Define severity groups
//     const severityGroups = {
//         'High Severity': ['MMHE', 'Emergency Exit'],
//         'Medium Plus Severity': ['Helmet', 'Vest'],
//         'Medium Severity': ['Machine Guard'],
//     };

//     // Color mapping for severity groups
//     const groupColors = {
//         'High Severity': '#ef4343',      // red
//         'Medium Plus Severity': '#f0ab43', // orange
//         'Medium Severity': '#40a1ff',    // blue
//     };

//     // Helper to get severity group for module
//     const getSeverityGroup = (category) => {
//         for (const group in severityGroups) {
//             if (severityGroups[group].includes(category)) return group;
//         }
//         return null; // ignore other categories or handle as you like
//     };

//     // State for chart options and series
//     const [chartOptions, setChartOptions] = useState({
//         chart: {
//             type: 'bar',
//             stacked: true,
//             toolbar: { show: false }
//         },
//         plotOptions: {
//             bar: {
//                 horizontal: false,
//                 columnWidth: '55%',
//                 endingShape: 'rounded',
//                 borderRadius: 8,
//                 borderRadiusApplication: 'end',
//                 borderRadiusWhenStacked: 'last'
//             }
//         },
//         dataLabels: {
//             enabled: true,
//             formatter: val => (val > 0 ? val : ''),
//             style: { fontSize: '12px', colors: ['#fff'] }
//         },
//         xaxis: {
//             categories: [] // modules will be added dynamically here
//         },
//         yaxis: {
//             title: { text: 'Alerts' },
//             min: 0,
//             forceNiceScale: true,
//         },
//         colors: [],
//         legend: {
//             position: 'bottom',
//             horizontalAlign: 'center',
//             offsetY: 0,
//             formatter: (seriesName) => seriesName
//         },
//         grid: {
//             borderColor: '#e7e7e7',
//             row: { colors: ['#f3f3f3', 'transparent'], opacity: 0.5 }
//         },
//         tooltip: {
//             y: { formatter: val => val }
//         }
//     });

//     const [chartSeries, setChartSeries] = useState([]);

//     // useEffect(() => {
//     //     if (
//     //         !moduleAnalytics ||
//     //         !Array.isArray(moduleAnalytics.modules) ||
//     //         !Array.isArray(moduleAnalytics.data)
//     //     ) {
//     //         console.log('Invalid or incomplete data, skipping...');
//     //         return;
//     //     }

//     //     if (moduleAnalytics.modules.length === 0 || moduleAnalytics.data.length === 0) {
//     //         console.log('No data modules or data to process, skipping...');
//     //         return;
//     //     }
//     //     console.log('backendModules:', moduleAnalytics.modules);
//     //     console.log('backendData:', moduleAnalytics.data);
//     //     const severityGroups = {
//     //         "High Severity": ["MMHE", "Emergency Exit"],
//     //         "Medium Plus Severity": ["Helmet", "Vest"],
//     //         "Medium Severity": ["Machine Guard"],
//     //     };
//     //     const groupColors = {
//     //         "High Severity": "#ef4343",
//     //         "Medium Plus Severity": "#f0ab43",
//     //         "Medium Severity": "#40a1ff",
//     //     };

//     //     const getGroup = (mod) => {
//     //         for (const group in severityGroups) {
//     //             if (severityGroups[group]?.includes(mod)) return group;
//     //         }
//     //         return null;
//     //     };

//     //     if (!moduleAnalytics || !moduleAnalytics?.modules || !moduleAnalytics?.data) return;
//     //     console.log('11111111111111111111111222222222222222222222222222')
//     //     // Backend modules/data -> [{mod, value, group}]
//     //     const safeArray = (arr) => (Array.isArray(arr) ? arr : []);
//     //     // const backendModules = moduleAnalytics?.modules;
//     //     // const backendData = moduleAnalytics?.data;
//     //     const backendModules = safeArray(moduleAnalytics.modules);
//     //     const backendData = safeArray(moduleAnalytics.data);


//     //     console.log('backendData', backendData)

//     //     // Step 1: Make array of objects with module, value, group
//     //     // const moduleList = backendModules?.map((mod, idx) => ({
//     //     //     module: mod,
//     //     //     value: backendData?.[idx],
//     //     //     group: getGroup(mod)
//     //     // }));
//     //     const moduleList = backendModules?.map((mod, idx) => ({
//     //         module: mod,
//     //         value: backendData[idx] ?? 0,  // fallback to 0 if undefined
//     //         group: getGroup(mod)
//     //     }));

//     //     // Step 2: Group and sort
//     //     let sorted = [
//     //         ...moduleList
//     //             ?.filter(x => x?.group === "High Severity")
//     //             ?.sort((a, b) => b.value - a.value),
//     //         ...moduleList
//     //             ?.filter(x => x?.group === "Medium Plus Severity")
//     //             ?.sort((a, b) => b.value - a.value),
//     //         ...moduleList
//     //             ?.filter(x => x?.group === "Medium Severity")
//     //             ?.sort((a, b) => b?.value - a?.value)
//     //     ];
//     //     console.log('111111111111111111111112222222222222222222222222223333333333333')

//     //     // Step 3: Series data arrays (in sorted order)
//     //     const sortedModules = sorted?.map(x => x?.module);

//     //     const groupData = {
//     //         "High Severity": [],
//     //         "Medium Plus Severity": [],
//     //         "Medium Severity": [],
//     //     };
//     //     console.log('44444444444444444444444444444444')
//     //     sorted?.forEach(({ module, value, group }) => {
//     //         Object.keys(groupData).forEach(g => {
//     //             if (g === group) {
//     //                 groupData[g].push(value ?? 0);  // safe fallback if value is undefined
//     //             } else {
//     //                 groupData[g].push(0);
//     //             }
//     //         });
//     //     });

//     //     // Step 4: Set series
//     //     const series = [
//     //         {
//     //             name: "High Severity",
//     //             data: groupData["High Severity"],
//     //             color: groupColors["High Severity"],
//     //         },
//     //         {
//     //             name: "Medium Plus Severity",
//     //             data: groupData["Medium Plus Severity"],
//     //             color: groupColors["Medium Plus Severity"],
//     //         },
//     //         {
//     //             name: "Medium Severity",
//     //             data: groupData["Medium Severity"],
//     //             color: groupColors["Medium Severity"],
//     //         },
//     //     ];
//     //     console.log('55555555555555555555555555555555555')
//     //     setChartSeries(series);
//     //     setChartOptions(prev => ({
//     //         ...prev,
//     //         xaxis: { ...prev.xaxis, categories: sortedModules },
//     //         colors: [groupColors["High Severity"], groupColors["Medium Plus Severity"], groupColors["Medium Severity"]],
//     //         legend: {
//     //             position: 'bottom',
//     //             horizontalAlign: 'center',
//     //             offsetY: 0,
//     //             formatter: (seriesName) => seriesName
//     //         }
//     //     }));

//     //     console.log('6666666666666666666666666666666666')

//     // }, [moduleAnalytics]);




//     // Handlers for area and sub-area selectors
//     useEffect(() => {
//         if (
//             !moduleAnalytics ||
//             !Array.isArray(moduleAnalytics.modules) ||
//             !Array.isArray(moduleAnalytics.data) ||
//             moduleAnalytics.modules.length === 0 ||
//             moduleAnalytics.data.length === 0
//         ) {
//             console.log('No valid data, skipping chart update');
//             return;
//         }

//         const severityGroups = {
//             "High Severity": ["MMHE", "Emergency Exit"],
//             "Medium Plus Severity": ["Helmet", "Vest"],
//             "Medium Severity": ["Machine Guard"],
//         };
//         const groupColors = {
//             "High Severity": "#ef4343",
//             "Medium Plus Severity": "#f0ab43",
//             "Medium Severity": "#40a1ff",
//         };

//         const getGroup = (mod) => {
//             for (const group in severityGroups) {
//                 if (severityGroups[group]?.includes(mod)) return group;
//             }
//             return null;
//         };

//         const safeArray = (arr) => (Array.isArray(arr) ? arr : []);
//         const backendModules = safeArray(moduleAnalytics.modules);
//         const backendData = safeArray(moduleAnalytics.data);

//         const moduleList = backendModules.map((mod, idx) => ({
//             module: mod,
//             value: backendData[idx] ?? 0,
//             group: getGroup(mod),
//         }));

//         const sorted = [
//             ...moduleList.filter(x => x.group === "High Severity").sort((a, b) => b.value - a.value),
//             ...moduleList.filter(x => x.group === "Medium Plus Severity").sort((a, b) => b.value - a.value),
//             ...moduleList.filter(x => x.group === "Medium Severity").sort((a, b) => b.value - a.value),
//         ];

//         const sortedModules = sorted.map(x => x.module);

//         const groupData = {
//             "High Severity": [],
//             "Medium Plus Severity": [],
//             "Medium Severity": [],
//         };

//         sorted.forEach(({ value, group }) => {
//             Object.keys(groupData).forEach(g => {
//                 if (g === group) {
//                     groupData[g].push(value ?? 0);
//                 } else {
//                     groupData[g].push(0);
//                 }
//             });
//         });


//         // Check length consistency before setting chart data
//         const allLengthsMatch = Object.values(groupData).every(arr => arr.length === sortedModules.length);

//         if (!allLengthsMatch) {
//             console.warn("Length mismatch between categories and series data. Skipping chart update.");
//             return;
//         }

//         const series = [
//             { name: "High Severity", data: groupData["High Severity"], color: groupColors["High Severity"] },
//             { name: "Medium Plus Severity", data: groupData["Medium Plus Severity"], color: groupColors["Medium Plus Severity"] },
//             { name: "Medium Severity", data: groupData["Medium Severity"], color: groupColors["Medium Severity"] },
//         ];

//         setChartSeries(series);
//         setChartOptions(prev => ({
//             ...prev,
//             xaxis: { ...prev.xaxis, categories: sortedModules },
//             colors: [groupColors["High Severity"], groupColors["Medium Plus Severity"], groupColors["Medium Severity"]],
//             legend: {
//                 position: 'bottom',
//                 horizontalAlign: 'center',
//                 offsetY: 0,
//                 formatter: seriesName => seriesName,
//             },
//         }));
//     }, [moduleAnalytics]);



//     const handleAreaChange = (e) => setSelectedArea(e.target.value);
//     const handleSubAreaChange = (e) => setSelectedSubArea(e.target.value);

//     return (
//         <Card className="mb-4" style={{ borderRadius: '32px' }}>
//             <CardHeader
//                 className="pb-0"
//                 style={{ background: '#f8f9fa', borderBottom: 'none', borderTopLeftRadius: '32px', borderTopRightRadius: '32px' }}
//             >
//                 <Row className="align-items-center">
//                     <Col>
//                         <h6 className="mb-0" style={{ color: '#333', fontWeight: '600' }}>
//                             Module Analytics
//                         </h6>
//                     </Col>
//                     <Col xs="auto">
//                         <FormGroup className="mb-0 d-flex">
//                             {!subAreaPage && (
//                                 <Input
//                                     type="select"
//                                     name="areaSelect"
//                                     id="areaSelect"
//                                     value={selectedArea}
//                                     onChange={handleAreaChange}
//                                     style={{
//                                         borderRadius: '4px',
//                                         border: '1px solid #ced4da',
//                                         padding: '8px 12px',
//                                         minWidth: '150px',
//                                         fontSize: '14px'
//                                     }}
//                                 >
//                                     {areas?.map(area => (
//                                         <option style={{ fontSize: '14px' }} key={area?.id} value={area?.id}>
//                                             {area?.area} - {area?.areaOwner || 'No Owner'}
//                                         </option>
//                                     ))}
//                                 </Input>
//                             )}
//                             {subAreaPage && (
//                                 <Input
//                                     type="select"
//                                     name="subAreaSelect"
//                                     id="subAreaSelect"
//                                     value={selectedSubArea}
//                                     onChange={handleSubAreaChange}
//                                     style={{
//                                         borderRadius: '4px',
//                                         border: '1px solid #ced4da',
//                                         padding: '8px 12px',
//                                         minWidth: '200px',
//                                         fontSize: '14px'
//                                     }}
//                                 >
//                                     {subAreas?.map(subArea => (
//                                         <option style={{ fontSize: '14px' }} key={subArea.id || 'all'} value={subArea.id}>
//                                             {subArea.name}
//                                         </option>
//                                     ))}
//                                 </Input>
//                             )}
//                         </FormGroup>
//                     </Col>
//                 </Row>
//             </CardHeader>
//             <CardBody>
//                 <div className="chart-container" style={{ marginTop: '20px', padding: '0 10px', position: 'relative' }}>
//                     {isLoading && (
//                         <div style={{
//                             position: 'absolute',
//                             top: 0,
//                             left: 0,
//                             right: 0,
//                             bottom: 0,
//                             display: 'flex',
//                             justifyContent: 'center',
//                             alignItems: 'center',
//                             backgroundColor: 'rgba(255, 255, 255, 0.7)',
//                             zIndex: 10
//                         }}>
//                             <div className="spinner-border text-primary" role="status">
//                                 <span className="sr-only">Loading...</span>
//                             </div>
//                         </div>
//                     )}
//                     {!isLoading && (!Array.isArray(moduleAnalytics.modules) || moduleAnalytics.modules.length === 0) ? (
//                         <div style={{
//                             display: 'flex',
//                             justifyContent: 'center',
//                             alignItems: 'center',
//                             height: '265px',
//                             color: '#666'
//                         }}>
//                             No data available for this area
//                         </div>
//                     ) : (
//                         <Chart
//                             options={chartOptions}
//                             series={chartSeries || []}
//                             type="bar"
//                             height={265}
//                         />
//                     )}
//                 </div>
//             </CardBody>
//         </Card>
//     );
// };

// export default BarChartForAreaanalysis;



// import React, { useState, useEffect } from 'react';
// import { Card, CardBody, CardHeader, Row, Col, FormGroup, Input } from 'reactstrap';
// import Chart from 'react-apexcharts';
// import AreaService from '../../../../../api/areaService';

// const BarChartForAreaanalysis = ({ chartData, callApi, filters, subAreaPage }) => {
//     // State for selected area filter
//     const [selectedArea, setSelectedArea] = useState('');
//     const [selectedSubArea, setSelectedSubArea] = useState('');
//     const [isLoading, setIsLoading] = useState(true);
//     const [areas, setAreas] = useState([]);
//     const [subAreas, setSubAreas] = useState([]);
//     const [moduleAnalytics, setModuleAnalytics] = useState({
//         totalAlerts: 0,
//         modules: [],
//         data: []
//     });

//     // Safe way to get user data with fallbacks
//     const getUserData = () => {
//         try {
//             const userData = JSON.parse(localStorage.getItem('userData'));
//             return {
//                 factoryId: userData?.factory?.id || 14,
//                 userId: userData?.id || 89
//             };
//         } catch (error) {
//             console.error('Error parsing userData from localStorage:', error);
//             return {
//                 factoryId: 14,
//                 userId: 89
//             };
//         }
//     };

//     const { factoryId, userId } = getUserData();

//     // Fetch all areas on component mount
//     useEffect(() => {
//         fetchAllAreas();
//     }, []);

//     // Fetch module analytics when selected area or sub-area changes
//     useEffect(() => {
//         if (selectedArea) {
//             if (subAreaPage) {
//                 fetchModuleAnalytics(selectedArea, selectedSubArea);
//             } else {
//                 fetchModuleAnalytics(selectedArea);
//             }
//         }
//     }, [selectedArea, selectedSubArea, callApi, subAreaPage]);

//     // Fetch all areas and sub-areas
//     const fetchAllAreas = async () => {
//         setIsLoading(true);
//         try {
//             const res = await AreaService.GetAllAreas(factoryId);
//             if (res?.data?.success && res?.data?.data?.areas) {
//                 const activeAreas = res.data.data.areas.filter(area => area.active);
//                 setAreas(activeAreas);
//                 if (activeAreas?.length > 0) {
//                     setSelectedArea(activeAreas[0].id);
//                 }

//                 if (subAreaPage) {
//                     const allSubAreas = [];
//                     activeAreas.forEach(area => {
//                         if (area.sub_area && Array.isArray(area.sub_area) && area.sub_area.length > 0) {
//                             area.sub_area.forEach(subArea => {
//                                 if (!allSubAreas.some(sa => sa.id === subArea.id)) {
//                                     allSubAreas.push(subArea);
//                                 }
//                             });
//                         }
//                     });
//                     allSubAreas.sort((a, b) => a.name.localeCompare(b.name));
//                     setSubAreas([{ id: "", name: "All" }, ...allSubAreas]);
//                     setSelectedSubArea("");
//                 }
//             }
//         } catch (error) {
//             console.error('Error fetching areas:', error);
//         } finally {
//             setIsLoading(false);
//         }
//     };

//     // Fetch module analytics by area or sub-area
//     const fetchModuleAnalytics = async (areaCode, subAreaCode = null) => {
//         setIsLoading(true);
//         try {
//             const payload = {
//                 weekly: filters?.week,
//                 month: filters?.month,
//                 shift: filters?.shifts,
//                 factory_id: factoryId,
//                 user_id: userId
//             };

//             if (subAreaPage) {
//                 if (subAreaCode && subAreaCode !== "") {
//                     payload.sub_area_id = parseInt(subAreaCode);
//                 } else {
//                     payload.safety_area_id = parseInt(areaCode);
//                 }
//             } else {
//                 payload.safety_area_id = parseInt(areaCode);
//             }

//             let res;
//             if (subAreaPage) {
//                 res = await AreaService.module_analytics_by_sub_area(payload);
//             } else {
//                 res = await AreaService.module_analytics_by_area(payload);
//             }

//             if (res?.data) {
//                 console.log('API Response:', res.data);

//                 // Safely extract and validate data
//                 const totalAlerts = res.data["Total Alerts"] ?? 0;
//                 const modules = Array.isArray(res.data.modules) ? res.data.modules : [];
//                 const data = Array.isArray(res.data.data) ? res.data.data : [];

//                 setModuleAnalytics({
//                     totalAlerts,
//                     modules,
//                     data
//                 });
//             } else {
//                 // Reset to empty state if no data
//                 setModuleAnalytics({
//                     totalAlerts: 0,
//                     modules: [],
//                     data: []
//                 });
//             }
//         } catch (error) {
//             console.error('Error fetching module analytics:', error);
//             // Reset to empty state on error
//             setModuleAnalytics({
//                 totalAlerts: 0,
//                 modules: [],
//                 data: []
//             });
//         } finally {
//             setIsLoading(false);
//         }
//     };

//     // Define severity groups and colors
//     const severityGroups = {
//         'High Severity': ['MMHE', 'Emergency Exit'],
//         'Medium Plus Severity': ['Helmet', 'Vest'],
//         'Medium Severity': ['Machine Guard'],
//     };

//     const groupColors = {
//         'High Severity': '#ef4343',
//         'Medium Plus Severity': '#f0ab43',
//         'Medium Severity': '#40a1ff',
//     };

//     // Helper to get severity group for module
//     const getSeverityGroup = (category) => {
//         for (const group in severityGroups) {
//             if (severityGroups[group].includes(category)) {
//                 return group;
//             }
//         }
//         return null;
//     };

//     // Initialize chart options with safe defaults
//     const getInitialChartOptions = () => ({
//         chart: {
//             type: 'bar',
//             stacked: true,
//             toolbar: { show: false }
//         },
//         plotOptions: {
//             bar: {
//                 horizontal: false,
//                 columnWidth: '55%',
//                 endingShape: 'rounded',
//                 borderRadius: 8,
//                 borderRadiusApplication: 'end',
//                 borderRadiusWhenStacked: 'last'
//             }
//         },
//         dataLabels: {
//             enabled: true,
//             formatter: val => (val > 0 ? val : ''),
//             style: { fontSize: '12px', colors: ['#fff'] }
//         },
//         xaxis: {
//             categories: []
//         },
//         yaxis: {
//             title: { text: 'Alerts' },
//             min: 0,
//             forceNiceScale: true,
//         },
//         colors: [groupColors['High Severity'], groupColors['Medium Plus Severity'], groupColors['Medium Severity']],
//         legend: {
//             position: 'bottom',
//             horizontalAlign: 'center',
//             offsetY: 0,
//             formatter: (seriesName) => seriesName
//         },
//         grid: {
//             borderColor: '#e7e7e7',
//             row: { colors: ['#f3f3f3', 'transparent'], opacity: 0.5 }
//         },
//         tooltip: {
//             y: { formatter: val => val }
//         }
//     });

//     const [chartOptions, setChartOptions] = useState(getInitialChartOptions());
//     const [chartSeries, setChartSeries] = useState([]);

//     // Process chart data when moduleAnalytics changes
//     useEffect(() => {
//         // Validate data before processing
//         if (
//             !moduleAnalytics ||
//             !Array.isArray(moduleAnalytics.modules) ||
//             !Array.isArray(moduleAnalytics.data) ||
//             moduleAnalytics.modules.length === 0
//         ) {
//             console.log('No valid data available for chart');
//             // Set empty chart data
//             setChartSeries([]);
//             setChartOptions(prev => ({
//                 ...prev,
//                 xaxis: { ...prev.xaxis, categories: [] }
//             }));
//             return;
//         }

//         try {
//             const backendModules = moduleAnalytics.modules;
//             const backendData = moduleAnalytics.data;

//             // Ensure both arrays have the same length
//             const maxLength = Math.max(backendModules.length, backendData.length);
//             const normalizedModules = backendModules.slice(0, maxLength);
//             const normalizedData = Array.from({ length: maxLength }, (_, i) => backendData[i] ?? 0);

//             // Create module list with groups
//             const moduleList = normalizedModules.map((mod, idx) => ({
//                 module: mod || `Module ${idx + 1}`, // Fallback for undefined modules
//                 value: normalizedData[idx],
//                 group: getSeverityGroup(mod),
//             }));

//             // Sort by severity groups and values
//             const sorted = [
//                 ...moduleList.filter(x => x.group === "High Severity").sort((a, b) => b.value - a.value),
//                 ...moduleList.filter(x => x.group === "Medium Plus Severity").sort((a, b) => b.value - a.value),
//                 ...moduleList.filter(x => x.group === "Medium Severity").sort((a, b) => b.value - a.value),
//                 ...moduleList.filter(x => x.group === null).sort((a, b) => b.value - a.value), // Handle ungrouped items
//             ];

//             if (sorted.length === 0) {
//                 console.log('No sorted data available');
//                 setChartSeries([]);
//                 setChartOptions(prev => ({
//                     ...prev,
//                     xaxis: { ...prev.xaxis, categories: [] }
//                 }));
//                 return;
//             }

//             const sortedModules = sorted.map(x => x.module);

//             // Initialize group data
//             const groupData = {
//                 "High Severity": [],
//                 "Medium Plus Severity": [],
//                 "Medium Severity": [],
//             };

//             // Populate group data arrays
//             sorted.forEach(({ value, group }) => {
//                 Object.keys(groupData).forEach(g => {
//                     if (g === group) {
//                         groupData[g].push(value);
//                     } else {
//                         groupData[g].push(0);
//                     }
//                 });
//             });

//             // Validate all arrays have the same length
//             const expectedLength = sortedModules.length;
//             const allLengthsMatch = Object.values(groupData).every(arr => arr.length === expectedLength);

//             if (!allLengthsMatch) {
//                 console.warn("Length mismatch between categories and series data");
//                 return;
//             }

//             // Create series data
//             const series = [
//                 {
//                     name: "High Severity",
//                     data: groupData["High Severity"],
//                 },
//                 {
//                     name: "Medium Plus Severity",
//                     data: groupData["Medium Plus Severity"],
//                 },
//                 {
//                     name: "Medium Severity",
//                     data: groupData["Medium Severity"],
//                 },
//             ].filter(s => s.data.some(val => val > 0)); // Only include series with data

//             setChartSeries(series);
//             setChartOptions(prev => ({
//                 ...prev,
//                 xaxis: { ...prev.xaxis, categories: sortedModules },
//             }));

//         } catch (error) {
//             console.error('Error processing chart data:', error);
//             // Reset to safe state on error
//             setChartSeries([]);
//             setChartOptions(prev => ({
//                 ...prev,
//                 xaxis: { ...prev.xaxis, categories: [] }
//             }));
//         }
//     }, [moduleAnalytics]);

//     const handleAreaChange = (e) => {
//         const value = e.target.value;
//         setSelectedArea(value);
//     };

//     const handleSubAreaChange = (e) => {
//         const value = e.target.value;
//         setSelectedSubArea(value);
//     };

//     // Check if we have valid chart data
//     const hasValidChartData = Array.isArray(chartSeries) && 
//                              chartSeries.length > 0 && 
//                              Array.isArray(chartOptions.xaxis.categories) && 
//                              chartOptions.xaxis.categories.length > 0;

//     return (
//         <Card className="mb-4" style={{ borderRadius: '32px' }}>
//             <CardHeader
//                 className="pb-0"
//                 style={{ 
//                     background: '#f8f9fa', 
//                     borderBottom: 'none', 
//                     borderTopLeftRadius: '32px', 
//                     borderTopRightRadius: '32px' 
//                 }}
//             >
//                 <Row className="align-items-center">
//                     <Col>
//                         <h6 className="mb-0" style={{ color: '#333', fontWeight: '600' }}>
//                             Module Analytics
//                         </h6>
//                     </Col>
//                     <Col xs="auto">
//                         <FormGroup className="mb-0 d-flex">
//                             {!subAreaPage && areas.length > 0 && (
//                                 <Input
//                                     type="select"
//                                     name="areaSelect"
//                                     id="areaSelect"
//                                     value={selectedArea}
//                                     onChange={handleAreaChange}
//                                     style={{
//                                         borderRadius: '4px',
//                                         border: '1px solid #ced4da',
//                                         padding: '8px 12px',
//                                         minWidth: '150px',
//                                         fontSize: '14px'
//                                     }}
//                                 >
//                                     {areas.map(area => (
//                                         <option 
//                                             style={{ fontSize: '14px' }} 
//                                             key={area?.id || Math.random()} 
//                                             value={area?.id}
//                                         >
//                                             {area?.area || 'Unknown Area'} - {area?.areaOwner || 'No Owner'}
//                                         </option>
//                                     ))}
//                                 </Input>
//                             )}
//                             {subAreaPage && subAreas.length > 0 && (
//                                 <Input
//                                     type="select"
//                                     name="subAreaSelect"
//                                     id="subAreaSelect"
//                                     value={selectedSubArea}
//                                     onChange={handleSubAreaChange}
//                                     style={{
//                                         borderRadius: '4px',
//                                         border: '1px solid #ced4da',
//                                         padding: '8px 12px',
//                                         minWidth: '200px',
//                                         fontSize: '14px'
//                                     }}
//                                 >
//                                     {subAreas.map(subArea => (
//                                         <option 
//                                             style={{ fontSize: '14px' }} 
//                                             key={subArea.id || 'all'} 
//                                             value={subArea.id}
//                                         >
//                                             {subArea.name || 'Unknown Sub Area'}
//                                         </option>
//                                     ))}
//                                 </Input>
//                             )}
//                         </FormGroup>
//                     </Col>
//                 </Row>
//             </CardHeader>
//             <CardBody>
//                 <div className="chart-container" style={{ marginTop: '20px', padding: '0 10px', position: 'relative' }}>
//                     {isLoading && (
//                         <div style={{
//                             position: 'absolute',
//                             top: 0,
//                             left: 0,
//                             right: 0,
//                             bottom: 0,
//                             display: 'flex',
//                             justifyContent: 'center',
//                             alignItems: 'center',
//                             backgroundColor: 'rgba(255, 255, 255, 0.7)',
//                             zIndex: 10
//                         }}>
//                             <div className="spinner-border text-primary" role="status">
//                                 <span className="sr-only">Loading...</span>
//                             </div>
//                         </div>
//                     )}
//                     {!isLoading && !hasValidChartData ? (
//                         <div style={{
//                             display: 'flex',
//                             justifyContent: 'center',
//                             alignItems: 'center',
//                             height: '265px',
//                             color: '#666'
//                         }}>
//                             No data available for this area
//                         </div>
//                     ) : !isLoading && hasValidChartData ? (
//                         <Chart
//                             options={chartOptions}
//                             series={chartSeries}
//                             type="bar"
//                             height={265}
//                         />
//                     ) : null}
//                 </div>
//             </CardBody>
//         </Card>
//     );
// };

// export default BarChartForAreaanalysis;
















import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, Row, Col, FormGroup, Input } from 'reactstrap';
import Chart from 'react-apexcharts';
import AreaService from '../../../../../api/areaService';

const BarChartForAreaanalysis = ({ chartData, callApi, filters, subAreaPage }) => {
    // State for selected area filter
    const [selectedArea, setSelectedArea] = useState('');
    const [selectedSubArea, setSelectedSubArea] = useState('');
    const [isLoading, setIsLoading] = useState(true);
    const [areas, setAreas] = useState([]);
    const [subAreas, setSubAreas] = useState([]);
    const [moduleAnalytics, setModuleAnalytics] = useState({
        totalAlerts: 0,
        modules: [],
        data: []
    });

    // Safe way to get user data with fallbacks
    const getUserData = () => {
        try {
            const userData = JSON.parse(localStorage.getItem('userData'));
            return {
                factoryId: userData?.factory?.id || 14,
                userId: userData?.id || 89
            };
        } catch (error) {
            console.error('Error parsing userData from localStorage:', error);
            return {
                factoryId: 14,
                userId: 89
            };
        }
    };

    const { factoryId, userId } = getUserData();

    // Fetch all areas on component mount
    useEffect(() => {
        fetchAllAreas();
    }, []);

    // Fetch module analytics when selected area or sub-area changes
    useEffect(() => {
        if (selectedArea) {
            if (subAreaPage) {
                fetchModuleAnalytics(selectedArea, selectedSubArea);
            } else {
                fetchModuleAnalytics(selectedArea);
            }
        }
    }, [selectedArea, selectedSubArea, callApi, subAreaPage]);

    // Fetch all areas and sub-areas
    const fetchAllAreas = async () => {
        setIsLoading(true);
        try {
            const res = await AreaService.GetAllAreas(factoryId);
            if (res?.data?.success && res?.data?.data?.areas) {
                const activeAreas = res.data.data.areas.filter(area => area.active);
                setAreas(activeAreas);
                if (activeAreas?.length > 0) {
                    setSelectedArea(activeAreas[0].id);
                }

                if (subAreaPage) {
                    const allSubAreas = [];
                    activeAreas.forEach(area => {
                        if (area.sub_area && Array.isArray(area.sub_area) && area.sub_area.length > 0) {
                            area.sub_area.forEach(subArea => {
                                if (!allSubAreas.some(sa => sa.id === subArea.id)) {
                                    allSubAreas.push(subArea);
                                }
                            });
                        }
                    });
                    allSubAreas.sort((a, b) => a.name.localeCompare(b.name));
                    setSubAreas([{ id: "", name: "All" }, ...allSubAreas]);
                    setSelectedSubArea("");
                }
            }
        } catch (error) {
            console.error('Error fetching areas:', error);
        } finally {
            setIsLoading(false);
        }
    };

    // Fetch module analytics by area or sub-area
    const fetchModuleAnalytics = async (areaCode, subAreaCode = null) => {
        setIsLoading(true);
        try {
            const payload = {
                weekly: filters?.week,
                month: filters?.month,
                shift: filters?.shifts,
                factory_id: factoryId,
                user_id: userId,
                start_date: filters?.starting || filters.date || '',
                end_date: filters?.ending || filters.date || '',
            };

            if (subAreaPage) {
                if (subAreaCode && subAreaCode !== "") {
                    payload.sub_area_id = parseInt(subAreaCode);
                } else {
                    payload.safety_area_id = parseInt(areaCode);
                }
            } else {
                payload.safety_area_id = parseInt(areaCode);
            }

            let res;
            if (subAreaPage) {
                res = await AreaService.module_analytics_by_sub_area(payload);
            } else {
                res = await AreaService.module_analytics_by_area(payload);
            }

            if (res?.data) { 

                // Safely extract and validate data
                const totalAlerts = res.data["Total Alerts"] ?? 0;
                const modules = Array.isArray(res.data.modules) ? res.data.modules : [];
                const data = Array.isArray(res.data.data) ? res.data.data : [];

                setModuleAnalytics({
                    totalAlerts,
                    modules,
                    data
                });
            } else {
                // Reset to empty state if no data
                setModuleAnalytics({
                    totalAlerts: 0,
                    modules: [],
                    data: []
                });
            }
        } catch (error) {
            console.error('Error fetching module analytics:', error);
            // Reset to empty state on error
            setModuleAnalytics({
                totalAlerts: 0,
                modules: [],
                data: []
            });
        } finally {
            setIsLoading(false);
        }
    };

    // Define severity groups and colors
    const severityGroups = {
        'High Severity': ['MMHE', 'Emergency Exit','No Go'],
        'Medium Plus Severity': ['Helmet', 'Vest'],
        'Medium Severity': ['Machine Guard'],
    };

    const groupColors = {
        'High Severity': '#ef4343',
        'Medium Plus Severity': '#f0ab43',
        'Medium Severity': '#40a1ff',
    };

    // Helper to get severity group for module
    const getSeverityGroup = (category) => {
        for (const group in severityGroups) {
            if (severityGroups[group].includes(category)) {
                return group;
            }
        }
        return null;
    };

    // Initialize chart options with safe defaults
    const getInitialChartOptions = () => ({
        chart: {
            type: 'bar',
            stacked: true,
            toolbar: { show: false }
        },
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: '55%',
                endingShape: 'rounded',
                borderRadius: 8,
                borderRadiusApplication: 'end',
                borderRadiusWhenStacked: 'last'
            }
        },
        dataLabels: {
            enabled: true,
            formatter: val => (val > 0 ? val : ''),
            style: { fontSize: '12px', colors: ['#fff'] }
        },
        xaxis: {
            categories: []
        },
        yaxis: {
            title: { text: 'Alerts' },
            min: 0,
            forceNiceScale: true,
        },
        colors: [groupColors['High Severity'], groupColors['Medium Plus Severity'], groupColors['Medium Severity']],
        legend: {
            position: 'bottom',
            horizontalAlign: 'center',
            offsetY: 0,
            formatter: (seriesName) => seriesName
        },
        grid: {
            borderColor: '#e7e7e7',
            row: { colors: ['#f3f3f3', 'transparent'], opacity: 0.5 }
        },
        tooltip: {
            y: { formatter: val => val }
        }
    });

    const [chartOptions, setChartOptions] = useState(getInitialChartOptions());
    const [chartSeries, setChartSeries] = useState([]);

    // Process chart data when moduleAnalytics changes
    useEffect(() => {
        // Validate data before processing
        if (
            !moduleAnalytics ||
            !Array.isArray(moduleAnalytics.modules) ||
            !Array.isArray(moduleAnalytics.data) ||
            moduleAnalytics.modules.length === 0
        ) {
            console.log('No valid data available for chart');
            // Set empty chart data
            setChartSeries([]);
            setChartOptions(prev => ({
                ...prev,
                xaxis: { ...prev.xaxis, categories: [] }
            }));
            return;
        }

        try {
            const backendModules = moduleAnalytics.modules;
            const backendData = moduleAnalytics.data;

            // Ensure both arrays have the same length
            const maxLength = Math.max(backendModules.length, backendData.length);
            const normalizedModules = backendModules.slice(0, maxLength);
            const normalizedData = Array.from({ length: maxLength }, (_, i) => backendData[i] ?? 0);

            // Create module list with groups
            const moduleList = normalizedModules.map((mod, idx) => ({
                module: mod || `Module ${idx + 1}`, // Fallback for undefined modules
                value: normalizedData[idx],
                group: getSeverityGroup(mod),
            }));

            // Sort by severity groups and values
            const sorted = [
                ...moduleList.filter(x => x.group === "High Severity").sort((a, b) => b.value - a.value),
                ...moduleList.filter(x => x.group === "Medium Plus Severity").sort((a, b) => b.value - a.value),
                ...moduleList.filter(x => x.group === "Medium Severity").sort((a, b) => b.value - a.value),
                ...moduleList.filter(x => x.group === null).sort((a, b) => b.value - a.value), // Handle ungrouped items
            ];

            if (sorted.length === 0) {
                console.log('No sorted data available');
                setChartSeries([]);
                setChartOptions(prev => ({
                    ...prev,
                    xaxis: { ...prev.xaxis, categories: [] }
                }));
                return;
            }

            const sortedModules = sorted.map(x => x.module);

            // Initialize group data
            const groupData = {
                "High Severity": [],
                "Medium Plus Severity": [],
                "Medium Severity": [],
            };

            // Populate group data arrays
            sorted.forEach(({ value, group }) => {
                Object.keys(groupData).forEach(g => {
                    if (g === group) {
                        groupData[g].push(value);
                    } else {
                        groupData[g].push(0);
                    }
                });
            });

            // Validate all arrays have the same length
            const expectedLength = sortedModules.length;
            const allLengthsMatch = Object.values(groupData).every(arr => arr.length === expectedLength);

            if (!allLengthsMatch) {
                console.warn("Length mismatch between categories and series data");
                return;
            }

            // Create series data - always include all series to maintain legend
            const series = [
                {
                    name: "High Severity",
                    data: groupData["High Severity"],
                },
                {
                    name: "Medium Plus Severity",
                    data: groupData["Medium Plus Severity"],
                },
                {
                    name: "Medium Severity",
                    data: groupData["Medium Severity"],
                },
            ]; // Keep all series to maintain consistent legend display

            setChartSeries(series);
            setChartOptions(prev => ({
                ...prev,
                xaxis: { ...prev.xaxis, categories: sortedModules },
            }));

        } catch (error) {
            console.error('Error processing chart data:', error);
            // Reset to safe state on error
            setChartSeries([]);
            setChartOptions(prev => ({
                ...prev,
                xaxis: { ...prev.xaxis, categories: [] }
            }));
        }
    }, [moduleAnalytics]);

    const handleAreaChange = (e) => {
        const value = e.target.value;
        setSelectedArea(value);
    };

    const handleSubAreaChange = (e) => {
        const value = e.target.value;
        setSelectedSubArea(value);
    };

    // Check if we have valid chart data
    const hasValidChartData = Array.isArray(chartSeries) &&
        chartSeries.length > 0 &&
        Array.isArray(chartOptions.xaxis.categories) &&
        chartOptions.xaxis.categories.length > 0;

    return (
        <Card className="mb-4" style={{ borderRadius: '32px' }}>
            <CardHeader
                className="pb-0"
                style={{
                    background: '#f8f9fa',
                    borderBottom: 'none',
                    borderTopLeftRadius: '32px',
                    borderTopRightRadius: '32px'
                }}
            >
                <Row className="align-items-center">
                    <Col>
                        <h6 className="mb-0" style={{ color: '#333', fontWeight: '600' }}>
                            Module Analytics
                        </h6>
                    </Col>
                    <Col xs="auto">
                        <FormGroup className="mb-0 d-flex">
                            {!subAreaPage && areas.length > 0 && (
                                <Input
                                    type="select"
                                    name="areaSelect"
                                    id="areaSelect"
                                    value={selectedArea}
                                    onChange={handleAreaChange}
                                    style={{
                                        borderRadius: '4px',
                                        border: '1px solid #ced4da',
                                        padding: '8px 12px',
                                        minWidth: '150px',
                                        fontSize: '14px'
                                    }}
                                >
                                    {areas.map(area => (
                                        <option
                                            style={{ fontSize: '14px' }}
                                            key={area?.id || Math.random()}
                                            value={area?.id}
                                        >
                                            {area?.area || 'Unknown Area'} - {area?.areaOwner || 'No Owner'}
                                        </option>
                                    ))}
                                </Input>
                            )}
                            {subAreaPage && subAreas.length > 0 && (
                                <Input
                                    type="select"
                                    name="subAreaSelect"
                                    id="subAreaSelect"
                                    value={selectedSubArea}
                                    onChange={handleSubAreaChange}
                                    style={{
                                        borderRadius: '4px',
                                        border: '1px solid #ced4da',
                                        padding: '8px 12px',
                                        minWidth: '200px',
                                        fontSize: '14px'
                                    }}
                                >
                                    {subAreas.map(subArea => (
                                        <option
                                            style={{ fontSize: '14px' }}
                                            key={subArea.id || 'all'}
                                            value={subArea.id}
                                        >
                                            {subArea.name || 'Unknown Sub Area'}
                                        </option>
                                    ))}
                                </Input>
                            )}
                        </FormGroup>
                    </Col>
                </Row>
            </CardHeader>
            <CardBody>
                <div className="chart-container" style={{ marginTop: '20px', padding: '0 10px', position: 'relative' }}>
                    {isLoading && (
                        <div style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            backgroundColor: 'rgba(255, 255, 255, 0.7)',
                            zIndex: 10
                        }}>
                            <div className="spinner-border text-primary" role="status">
                                <span className="sr-only">Loading...</span>
                            </div>
                        </div>
                    )}  
                    {!isLoading && !hasValidChartData ? (
                        <div style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            height: '265px',
                            color: '#666'
                        }}>
                            No data available for this area
                        </div>
                    ) : !isLoading && hasValidChartData ? (
                        <Chart
                            options={chartOptions}
                            series={chartSeries}
                            type="bar"
                            height={265}
                        />
                    ) : null}
                </div>
            </CardBody>
        </Card>
    );
};

export default BarChartForAreaanalysis;

