import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON>ody, ModalFooter, Button, FormGroup, Label, Input, Spinner, FormFeedback } from 'reactstrap';

export default function AddSubAreaModal({ isOpen, toggle, areaOptions, onSubmit, loading,form ,setForm,modaltype}) {

  const [errors, setErrors] = useState({});

  const validate = () => {
    const newErrors = {};
    if (!form.area_id) newErrors.area_id = 'Area is required';
    if (!form.name.trim()) newErrors.name = 'Sub-Area Name is required';
    if (!form.address.trim()) newErrors.address = 'Address is required';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value, files } = e.target;
    setForm(prev => ({
      ...prev,
      [name]: files ? files[0] : value,
    }));
    setErrors(prev => ({ ...prev, [name]: undefined }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!validate()) return; 
    onSubmit(form, () => setForm({ area_id: '', name: '', address: '', file: null }));
  };
 

  return (
    <Modal isOpen={isOpen} toggle={toggle}>
      <ModalHeader toggle={toggle}>{modaltype === 'add' ? 'Add' : 'Edit'} Sub-Area</ModalHeader>
      <ModalBody>
        <FormGroup>
          <Label for="area_id">Area</Label>
          <Input
            type="select"
            name="area_id"
            id="area_id"
            value={form.area_id}
            onChange={handleChange}
            invalid={!!errors.area_id}
            required
          >
            <option value="">Select Area</option>
            {areaOptions.map(area => (
              <option key={area.area_id} value={area.id || area.area_id}>
                {area.area || area.area_name}
              </option>
            ))}
          </Input>
          {errors.area_id && <FormFeedback>{errors.area_id}</FormFeedback>}
        </FormGroup>
        <FormGroup>
          <Label for="name">Sub-Area Name</Label>
          <Input
            type="text"
            name="name"
            id="name"
            value={form.name}
            onChange={handleChange}
            invalid={!!errors.name}
            required
          />
          {errors.name && <FormFeedback>{errors.name}</FormFeedback>}
        </FormGroup>
        <FormGroup>
          <Label for="address">Address</Label>
          <Input
            type="text"
            name="address"
            id="address"
            value={form.address}
            onChange={handleChange}
            invalid={!!errors.address}
            required
          />
          {errors.address && <FormFeedback>{errors.address}</FormFeedback>}
        </FormGroup>
        <FormGroup>
          <Label for="file">Logo (optional)</Label>
          <Input
            type="file"
            name="file"
            id="file"
            onChange={handleChange}
            accept="image/*"
          />
        </FormGroup>
        {form.file instanceof File && (
          <div className="mt-2 text-center">
            <img
              src={URL.createObjectURL(form.file)}
              alt="Preview"
              className="img-thumbnail"
              style={{ maxWidth: '120px', maxHeight: '120px' }}
            />
          </div>
        )}
      </ModalBody>
      <ModalFooter>
        <Button color="primary" onClick={handleSubmit} disabled={loading}>
          {loading ? <Spinner size="sm" /> : 'Add Sub-Area'}
        </Button>
        <Button color="secondary" onClick={toggle} disabled={loading}>Cancel</Button>
      </ModalFooter>
    </Modal>
  );
}