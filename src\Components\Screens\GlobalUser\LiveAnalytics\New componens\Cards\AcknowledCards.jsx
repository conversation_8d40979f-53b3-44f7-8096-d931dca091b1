// import React from 'react'
// import { Card, CardBody } from 'reactstrap';
// import { FaArrowTrendUp, FaArrowDown } from "react-icons/fa6";
// import { SiOpenai } from "react-icons/si";
// import Loader3 from '../../../../../../CommonElements/Spinner/loader3';
// import { ArrowDown } from 'react-feather';
// import { useNavigate } from 'react-router';

// const AcknowledCards = ({ data, loader, filters }) => {
//   let accuracyPercent = data
// console.log('accuracy percent compo', data)
//   const Navigate = useNavigate()

//   const handleNavigate = () => {
//     const aiFilters = {
//       areas: [JSON.parse(localStorage.getItem("userData"))?.area_ids?.name],
//       shifts: filters?.shift ? [filters?.shift]: [],
//       date: "",
//       week: filters?.weekly,
//       month: filters?.month,
//       starting: "",
//       ending: ""
//     }
//     console.log('aifiltersss', aiFilters)
//     localStorage.setItem('aifilters', JSON.stringify(aiFilters));
//     const url = `${process.env.PUBLIC_URL}/reports/${JSON.parse(localStorage.getItem('role'))}`;
//     Navigate(url);
//   }
//   return (
//     <Card onClick={handleNavigate} style={{ borderRadius: '24px', minHeight: '170px', maxHeight: 'auto', cursor: 'pointer' }}>
//       {loader ? <span className="w-100 h-100 d-flex justify-content-center align-items-center position-absolute"><Loader3 /></span> :
//         <CardBody className='p-4'>
//           <p style={{ fontSize: '16px', color: '#383838', fontWeight: '400' }} className="ellipsis-text">AI Accuracy</p>
//           <div className=' d-flex align-items-center gap-2 '>
//             <SiOpenai style={{ width: '40px', height: '40px', borderRadius: '20px', padding: '5px', background: '#175FA4', color: 'white' }} />
//             <p className=' m-0' style={{ color: '#595959', fontSize: '16px', fontSize: '30px', fontWeight: '500' }} >
//               {accuracyPercent === "NaN"
//                 ? "N/A"
//                 : !accuracyPercent
//                   ? "N/A"
//                   : accuracyPercent + "%"}
//             </p>
//           </div>
//         </CardBody>
//       }
//     </Card>
//   )
// }
// export default AcknowledCards




import React from 'react'
import { Card, CardBody } from 'reactstrap';
import { FaArrowTrendUp, FaArrowDown } from "react-icons/fa6";
import { SiOpenai } from "react-icons/si";
import Loader3 from '../../../../../../CommonElements/Spinner/loader3';
import { ArrowDown } from 'react-feather';
import { PiWarningOctagon } from 'react-icons/pi';
import { IoWarning } from 'react-icons/io5';
import { useNavigate } from 'react-router';
import { Link } from 'react-router-dom';

const AcknowledCards = ({ data, loader, filters, area }) => {

    const factoryName = JSON.parse(localStorage.getItem('userData'))?.factory?.name
      const Navigate = useNavigate()
      
      const handleNavigate = (type) => {
        const navigateData = {
        //   area: AreaDashboard ? "" : maxValue?.violationArea ? maxValue?.violationArea : '',
          area: area ? JSON.parse(localStorage.getItem('userData')).area_ids.name : "",
        //   subarea: !AreaDashboard ? "" : maxValue?.violationArea ? maxValue?.violationArea : '',
          // shift: [`Shift ${shift}`] || [filters?.shift],
          subarea:"",
          shift: filters?.shift ? [filters?.shift] : [],
          // shift:[],
          severity: '',
        //   module: maxValue?.violationType ? maxValue?.violationType : '',
            module: ''
    }  
        const liveFilters = {
            ...navigateData,
          acknowledge: type=='pending' ? 'false' : type=='total' ? '' : type=='acknowledged' && 'true' ,
          approval: "Select Approval",
          date: "",
          week: filters?.weekly || '',
          month: filters?.month || '',
          starting: "",
          ending: ""
        }
        localStorage.setItem('high_severity_alerts', JSON.stringify(liveFilters));
        const url = `${process.env.PUBLIC_URL}/default/high_severity_alerts/${JSON.parse(localStorage.getItem('role'))}`;
        Navigate(url);
      }
    return (
        <Card
            style={{
                borderRadius: '24px',
                minHeight: '170px',
                maxHeight: '170px',
                background: 'white',
            }}
        >

            <CardBody className='px-4 py-4' style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                {/* Total Count */}
                {
                    loader ?
                        <span className=" h-100 d-flex justify-content-center align-items-center ">
                            <Loader3 />
                        </span> : <>
                            <div className="d-flex align-items-center gap-3 mb-3">
                                <div style={{
                                    width: '50px',
                                    height: '50px',
                                    borderRadius: '12px',
                                    background: '#fbbf24',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    position:"relative"
                                }}>
                                    <IoWarning style={{
                                        width: '18px',
                                        height: '18px',
                                        color: 'white'
                                    }} />
                                </div>

                                <div style={{cursor:'pointer'}} onClick={()=> {
                                    factoryName == 'RYK' &&  handleNavigate('total')
                                   
                                }
                                    }>
                                    <p style={{
                                        color: '#6b7280',
                                        fontSize: '14px',
                                        margin: '4px 0 0 0',
                                        fontWeight: '500'
                                    }}>
                                        Total Alerts
                                    </p>
                                    <p style={{
                                        color: '#1f2937',
                                        fontSize: '18px',
                                        fontWeight: '700',
                                        margin: '0',
                                        lineHeight: '1'
                                    }}>
                                        {data?.totalRecords || 'N/A'}
                                    </p>
                                         
                                </div>
                                {/* <Link>
                                </Link> */}
                                    <span className='f-light ms-2' style={{fontSize:'12px',position:'absolute',top:'55px',right:'20px'}}> <b>Pending: {data?.pending || 0}</b> </span>
                                    
                            </div>

                            {/* Alert Breakdown */}
                            <div className="row g-2 flex-grow-1">
                                {/* Acknowledged Alerts */}
                                <div className="col-6">
                                    <div style={{
                                        background: '#f0fdf4',
                                        borderRadius: '12px',
                                        padding: '12px',
                                        border: '1px solid #bbf7d0',
                                        height: '100%',
                                        minWidth: '110px',
                                        maxWidth: 'auto',
                                        cursor:'pointer'
                                    }} onClick={()=> {
                                    factoryName == 'RYK' &&  handleNavigate('acknowledged')
                                   
                                }
                                    }>
                                        <div className="d-flex align-items-center gap-2 mb-2">
                                            <span style={{
                                                color: '#065f46',
                                                fontSize: '12px',
                                                fontWeight: '600'
                                            }}>
                                                Acknowledged
                                            </span>
                                        </div>
                                        <p style={{
                                            color: '#047857',
                                            fontSize: '24px',
                                            fontWeight: '700',
                                            margin: '0',
                                            lineHeight: '1'
                                        }}>
                                            {data?.acceptedRecords || '0'}
                                        </p>
                                    </div>
                                </div>

                                {/* Non-Acknowledged Alerts */}
                                <div   className="col-6">
                                    <div onClick={()=> {
                                    factoryName == 'RYK' &&  handleNavigate('pending')
                                   
                                }
                                    } style={{
                                        background: '#fef2f2',
                                        borderRadius: '12px',
                                        padding: '12px',
                                        border: '1px solid #fecaca',
                                        height: '100%',
                                        minWidth: '110px',
                                        maxWidth: 'auto',
                                        cursor:'pointer'
                                    }}>
                                        <div className="d-flex align-items-center gap-2 mb-2">
                                            <span style={{
                                                color: '#991b1b',
                                                fontSize: '12px',
                                                fontWeight: '600'
                                            }}>
                                                Not an alert
                                            </span>
                                        </div>
                                        <p style={{
                                            color: '#dc2626',
                                            fontSize: '24px',
                                            fontWeight: '700',
                                            margin: '0',
                                            lineHeight: '1'
                                        }}>
                                            {data?.not_an_alert || '0'}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </>
                }

            </CardBody>

        </Card>
    )
}

export default AcknowledCards