import React, { useState, useEffect, useRef } from 'react';
import './chatbox.css';
import { AiOutlineMinusCircle } from "react-icons/ai";
import ChatBoxAvatar from '../assets/chatbox/ChatBoxAvatar.png';
import ToolTipImg from '../assets/chatbox/ToolTip.png';
import ChatboxBody from './Component/ChatboxBody';

const DisruptLabChat = () => {
    const [isOpen, setIsOpen] = useState(false);
    const chatContainerRef = useRef(null);
    const avatarRef = useRef(null);

    const [prompt, setPrompt] = useState('');
    const [messages, setMessages] = useState([]);
    const [loading, setLoading] = useState(false);
    const [showFullInput, setShowFullInput] = useState(false);
    const [isHovering, setIsHovering] = useState(false);

    // For dragging
    const [position, setPosition] = useState({ bottom: 20, right: 20 });
    const [isDragging, setIsDragging] = useState(false);
    const [dragStartPos, setDragStartPos] = useState({ x: 0, y: 0 });
    const offset = useRef({ x: 0, y: 0 });

    const toggleChat = () => {
        if (isOpen) {
            // Reset to default when closing
            setPosition({ bottom: 20, right: 20 });
        }
        setIsOpen(!isOpen);
        setShowFullInput(true);
    };

    const handleMouseDown = (e) => {
        // Only allow dragging when chat is closed
        if (isOpen) return;
        
        // Record start position to detect if it's a click vs drag
        setDragStartPos({ x: e.clientX, y: e.clientY });
        
        const element = chatContainerRef.current;
        const rect = element.getBoundingClientRect();

        offset.current = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        };

        setIsDragging(true);
        e.preventDefault();
    };

    const handleMouseMove = (e) => {
        if (!isDragging) return;

        const element = chatContainerRef.current;
        const rect = element.getBoundingClientRect();
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;

        let newLeft = e.clientX - offset.current.x;
        let newTop = e.clientY - offset.current.y;

        // Boundary checks to keep element within viewport
        newLeft = Math.max(0, Math.min(newLeft, windowWidth - rect.width));
        newTop = Math.max(0, Math.min(newTop, windowHeight - rect.height));

        setPosition({
            left: newLeft,
            top: newTop,
            bottom: undefined,
            right: undefined
        });
    };

    const handleMouseUp = (e) => {
        if (!isDragging) return;
        
        setIsDragging(false);
        
        // Check if it was a click (minimal movement)
        const moveThreshold = 5; // pixels
        const movedX = Math.abs(e.clientX - dragStartPos.x);
        const movedY = Math.abs(e.clientY - dragStartPos.y);
        
        if (movedX < moveThreshold && movedY < moveThreshold) {
            // It's a click, open chat at initial position
            setPosition({ bottom: 20, right: 20 });
            setIsOpen(true);
        }
    };

    useEffect(() => {
        if (isDragging) {
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
        }
        
        return () => {
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        };
    }, [isDragging]);

    return (
        <div
            className="disrupt-chat-container"
            ref={chatContainerRef}
            onMouseDown={handleMouseDown}
            style={{
                position: 'fixed',
                zIndex: 1000,
                cursor: isDragging ? 'grabbing' : (!isOpen ? 'grab' : 'auto'),
                ...(position.left !== undefined
                    ? { left: position.left, top: position.top }
                    : { bottom: position.bottom, right: position.right }),
                touchAction: 'none' // Prevent touch scrolling on mobile
            }}
        >
            {/* Closed Help Button */}
            {!isOpen && (
                <div
                    className="help-button-container"
                    onMouseEnter={() => setIsHovering(true)}
                    onMouseLeave={() => setIsHovering(false)}
                    ref={avatarRef}
                    style={{ position: 'relative' }}
                >
                    {isHovering && (
                        <img src={ToolTipImg} alt="Tooltip" style={{ 
                            width: '120px', 
                            height: '50px', 
                            position: 'absolute', 
                            bottom: '80px', 
                            right: '50px',
                            pointerEvents: 'none' // Make tooltip non-interactive
                        }} />
                    )}
                    <button 
                        className="help-button"
                        style={{ pointerEvents: isDragging ? 'none' : 'auto' }} // Disable button during drag
                    >
                        <img src={ChatBoxAvatar} alt="Help" />
                    </button>
                </div>
            )}

            {/* Chat Box */}
            <div className="chat-box-container">
                <div className={`chat-box ${isOpen ? 'open' : ''}`}>
                    {isOpen && (
                        <>
                            <div className="chat-header">
                                <div>
                                    <button className="headerAvatar" onClick={toggleChat}>
                                        <img src={ChatBoxAvatar} alt="Help Support" />
                                    </button>
                                    <span style={{ fontFamily: "monospace" }}>Unilever Chatbot</span>
                                </div>
                                <AiOutlineMinusCircle size='20px' color='white' className='me-2' onClick={toggleChat} style={{ cursor: 'pointer' }} />
                            </div>
                            <div className='b-r-10 my-0 w-100' style={{ background: 'white', color: "#3470a1" }}>
                                <p className='f-light mt-3 mx-4' style={{ fontSize: '12px' }}>
                                    <b>Disclaimer:</b> This chat assistant is currently configured to query <b>"Policies and Management"</b> SHE - Standards and Guidelines.
                                </p>
                            </div>
                            <div className="chatboxbody">
                                <ChatboxBody
                                    setLoading={setLoading}
                                    prompt={prompt}
                                    setPrompt={setPrompt}
                                    messages={messages}
                                    setMessages={setMessages}
                                    loading={loading}
                                    setShowFullInput={setShowFullInput}
                                    showFullInput={showFullInput}
                                />
                            </div>
                        </>
                    )}
                </div>
            </div>
        </div>
    );
};

export default DisruptLabChat;


 