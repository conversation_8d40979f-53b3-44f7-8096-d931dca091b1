import styled from 'styled-components';
import { Card, Input } from 'reactstrap';

// Styled components for better UI
export const StyledCard = styled(Card)`
  border-radius: 12px;
  box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.1);
  transition: all 0.3s ease;
  &:hover {
    box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.2);
  }
`;

export const StyledSelect = styled(Input)`
  border-radius: 8px;
  border: 1px solid #dfe3e7;
  padding: 0.6rem 1rem;
  &:focus {
    border-color: #7367f0;
    box-shadow: 0 3px 10px 0 rgba(115, 103, 240, 0.1);
  }
`;

export const EmptyStateContainer = styled.div`
  height: 40vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 2rem;
  background-color: #f8f8f8;
  border-radius: 12px;
  margin: 1rem 0;
`;

export const EmptyStateIcon = styled.div`
  font-size: 3rem;
  color: #b8c2cc;
  margin-bottom: 1rem;
`;

export const StatsCard = styled.div`
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.05);
`;

export const StatsIcon = styled.div`
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  background: ${props => props.color || '#7367f0'};
  color: white;
  font-size: 1.5rem;
`;

export const StatsContent = styled.div`
  flex: 1;
`;

export const StatsTitle = styled.p`
  margin: 0;
  color: #6e6b7b;
  font-size: 0.875rem;
`;

export const StatsValue = styled.h3`
  margin: 0;
  color: #5e5873;
  font-weight: 600;
`;