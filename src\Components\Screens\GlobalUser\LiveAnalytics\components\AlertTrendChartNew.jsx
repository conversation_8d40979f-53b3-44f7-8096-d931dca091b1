import React, { use<PERSON>ontext, useEffect, useState } from 'react'
import { Card, CardBody, CardHeader, Col, Row } from 'react-bootstrap'
import CommonFIlterButton from '../../../../Common/commonFilterButton/CommonFIlterButton'
// import { Shifts, monthlyDataa, weeklyData, shiftData, dummyJSON } from "../../../../Data/staticData/data";
import { Shifts, monthlyDataa, weeklyData, shiftData, dummyJSON } from "../../../../../Data/staticData/data";
import { Input } from 'reactstrap'
import { TurnoverChart } from '../analytics_data_temp';
import { formatMonth, formatWeek } from '../../../../../utils/formatDate';
import { errorToast, getWeek } from "../../../../../_helper/helper";
import Chart from "react-apexcharts";
import './alertTrend.css'
import { analyticsPageService } from '../../../../../api/analyticsPageService';
import { getCurrentWeekWithYear } from '../../../../../utils/currentWeekWithYear';
// import Loader3 from '../../../../CommonElements/Spinner/loader3'
import Loader3 from '../../../../../CommonElements/Spinner/loader3'
import liveanalyticcontext from '../../../../../_helper/formData/LiveAnalytics/LiveAnalytics'

const AlertTrendChartNew = ({ ref, chartData, area_ID, filters }) => {
  const [TurnoverChartState, setTurnoverChartState] = useState(TurnoverChart)
  const [factoryID, setfactoryID] = useState(JSON.parse(localStorage.getItem('userData')).factory.id || 0)
  const [chartLoader, setChartLoader] = useState(false)
  const { alerttrendcontext, setalerttrenddatacontext } = useContext(liveanalyticcontext)
  const [totalAlerts, setTotalAlerts] = useState(0);


  useEffect(() => { 
    setChartLoader(true);
    alertsTrend();


  }, [filters]);

  async function alertsTrend() {

    const area_user = JSON.parse(localStorage.getItem('role'))
    const fils = JSON.parse(localStorage.getItem('FactoryChartsFilters')) || filters
    const userArea = area_ID ? area_ID : area_user == 'area' ? JSON.parse(localStorage.getItem('userData')).area_ids.name : "";
    let payload;
    console.log("-------------------------xd-------------------")
    console.log(userArea)
    console.log(fils)
    console.log(filters)
    if (userArea) {
      console.log("User area found")
      if (fils) {
        console.log("filters got from local storage")
        payload = {
          ...fils,
          area: userArea,
          factory_id: factoryID,
        };
      }
      else {
        payload = {
          area: userArea,
          factory_id: factoryID, 
        };
      }
    }


    else {
      if (fils) {
        payload = {
          ...fils,
          factory_id: factoryID,
          // weekly: filters.weekly,
        };
      }
      else {
        payload = {
          ...filters,
          factory_id: factoryID,
        };
      } 


    }
    const controller = new AbortController();

    try {
      const res = await analyticsPageService.alertsTrend(payload, { signal: controller.signal })
      if (res.statusText.toLowerCase() == 'ok') {
        const updatedcategories = res?.data?.categories.map((category, index) => {
          // return Math.ceil(index + 1);  
          return category
        });
        const filteredSeries = res?.data?.series?.filter(item => item.name !== "Total");
         console.log('res?.data?.series', res?.data?.series)
        const findTotal = res?.data?.series?.find((item) => item?.name === 'Total')?.data?.reduce((a, b) => a + b, 0);
        setTotalAlerts(findTotal)
        setalerttrenddatacontext(res?.data?.series)
        if (filters.month) {
          setTurnoverChartState({
            ...TurnoverChartState,
            series: res?.data?.series,
            options: {
              ...TurnoverChartState.options,
              xaxis: {
                categories: updatedcategories,
                labels: {
                  rotate: -45,
                  style: {
                    fontSize: window.innerWidth <= 768 ? '8px' : '12px',
                  },
                  formatter: function (value) {
                    // Find the index of the current value
                    const index = updatedcategories.indexOf(value); 
                    // Show every 3rd label in the mobile view
                    if (window.innerWidth <= 768) {
                      return index % 3 === 0 ? value : '';
                    }
                    return Math.floor(value);
                  },
                },
              },
              dataLabels: {
                enabled: true,
                formatter: function(val, { seriesIndex, dataPointIndex }) {
                  return res?.data?.series[seriesIndex].data[dataPointIndex];
                },
                style: {
                  fontSize: '8px',
                  colors: ["#000"]
                }
              },
              yaxis: {
                labels: {
                  formatter: function (value) {
                    return Math.round(value); // Round float values to whole numbers
                  },
                  style: {
                    fontSize: window.innerWidth <= 768 ? '8px' : '12px',
                  }
                },
                min: 0, // Ensure the min value starts at 0
                // max: Math.ceil(Math.max(...filteredSeries.flatMap(s => s.data)) * 1.2), // Increase max limit
                forceNiceScale: true,
              },
              tooltip: {
                enabled: true,
                x: {
                  formatter: function (value) {
                    return 'Day ' + value;
                  },
                },
              },
            },
          });
        } else {
          setTurnoverChartState(prevState => ({
            ...prevState,
            series: res?.data?.series,
            options: {
              ...prevState.options,
              // xaxis: {
              //   ...prevState.options.xaxis,
              //   categories: updatedcategories
              // }
              xaxis: {
                ...prevState.options.xaxis,
                categories: updatedcategories,
                labels: {
                  rotate: -45,
                  style: {
                    fontSize: window.innerWidth <= 768 ? '8px' : '12px',
                  },
                  // formatter: function (value) {
                  //   return Math.floor(value).toFixed(0);
                  // },
                },
              },
              dataLabels: {
                enabled: true,
                formatter: function(val, { seriesIndex, dataPointIndex }) {
                  return res?.data?.series[seriesIndex].data[dataPointIndex];
                },
                style: {
                  fontSize: '8px',
                  colors: ["#36454F"]
                }
              },
              yaxis: {
                labels: {
                  formatter: function (value) {
                    return Math.round(value); // Round float values to whole numbers
                  },
                  style: {
                    fontSize: window.innerWidth <= 768 ? '8px' : '12px',
                  }
                },
                min: 0,
                // tickAmount: Math.ceil(Math.max(...filteredSeries.flatMap(s => s.data))) + 1, // Fix for repeating numbers
                forceNiceScale: true,
              }
            }
          }));
        }

        

        setChartLoader(false)
      }
    } catch (err) {
      console.log('Alerts Trend Chart Error', err)
      setChartLoader(false)
      return () => controller.abort(); // Abort previous request on cleanup
    }

  }

  return (
    <Card ref={alerttrendcontext} style={{ borderRadius: '32px', height: '399px' }}>

      <h5 className="px-4 py-3" style={{ fontWeight: '500', color: '#383838', }}>Alerts Trend  <div>
        <span style={{ fontSize: '15px' }}>Total Alerts :  {chartLoader ? '...' : totalAlerts}</span>
      </div></h5>


      {chartLoader ? <div className='w-100 h-100 d-flex justify-content-center align-items-center position-absolute ' style={{ height: '100%' }} ><Loader3 /></div> : <>
        <CardBody>
          <Chart
            options={TurnoverChartState.options}
            series={TurnoverChartState.series}
            height={'268px'}
          />
        </CardBody>

      </>}

    </Card>
  )
}

export default AlertTrendChartNew





