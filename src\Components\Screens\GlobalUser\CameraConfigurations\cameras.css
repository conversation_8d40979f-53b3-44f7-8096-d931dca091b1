.col-right {
  padding-right: 20px;
}

.col-left {
  padding-left: 20px;
  border-left: 1px solid lightgray;
}
.col-border {
  border-left: 1px solid lightgray;
  padding-left: 20px;
}

.camera_image {
  opacity: 0; /* Start fully transparent */
  transition: opacity 0.5s ease-in-out; /* Adjust transition duration as needed */
}

.camera_image.visible {
  opacity: 1; /* Fade to fully visible */
}
.loader-for-image {
  position: absolute;
  top: 20px;
  right: 49px;
}

@media (max-width: 1200px) {
  .col-left {
    border: none;
    padding-left: 15px;
  }
}

@media (max-width: 992px) {
  .col-border {
    border-left: none;
    padding-left: 0px;
  }
  .col-right {
    padding-right: 0px;
    padding-left: 0px;
  }
  .col-left {
    border: none;
    padding-left: 0px;
  }
}

.camera_item_card_odd {
  margin: 5px -7px 5px 0px;
}
.camera_item_card_even {
  margin: 5px 0px 5px -7px;
}
 .custom-typeahead .rbt-input::-webkit-scrollbar,
.custom-typeahead .rbt-menu-dropdown::-webkit-scrollbar {
  display: none; 
} 

/* Hide scrollbar in Firefox */
.custom-typeahead .rbt-input {
  scrollbar-width: none;
  height: auto;

}

.custom-typeahead .rbt-menu-dropdown {
  scrollbar-width: none; 
}

/* Ensure overflow is still active, so users can scroll */
.custom-typeahead .rbt-input,
.custom-typeahead .rbt-menu-dropdown {
  overflow: none; 
}  

.custom-typeahead .rbt-input-multi {
  cursor: text;
  overflow-y: auto;
  max-height: auto !important;
  align-items: start;
  vertical-align: top;
  border-radius: 8px;
}


.ellipsis-text {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

.ellipsis-text:hover,
.ellipsis-text:focus {
  white-space: normal;
  overflow: visible;
}

@media (max-width: 1400px) {
  .camera_item_card_odd {
    margin: 5px 0px;
  }
  .camera_item_card_even {
    margin: 5px 0px;
  }
}

/* @media (max-width: 767px) {
    .camera_item_card_odd{
        margin:5px 0px;
    }
    .camera_item_card_even{
        margin:5px 0px;
    }
   }

   @media (max-width: 1120px) and (min-width: 993px) {
    .camera_item_card_odd{
        margin:5px 0px;
    }
    .camera_item_card_even{
        margin:5px 0px;
        
    }
    
    }

@media (max-width: 898px) and (min-width: 590px) {
    .camera_item_card_odd{
        margin:5px 0px;
    }
    .camera_item_card_even{
        margin:5px 0px;
    }
    
    } */

.menu-title {
  font-size: 14px;
  color: black;
  display: "inline-block";
}

/* Camera Details Modal Styles */
.camera-details-modal .modal-content {
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
  border: none;
  overflow: hidden;
}

.camera-details-modal .modal-header {
  background-color: #f8f9fa;
  padding: 18px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.camera-details-modal .modal-body {
  padding: 24px;
  background-color: #ffffff;
}

/* Section Styling */
.camera-details-container .info-section {
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
}

.camera-details-container .info-section:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.camera-details-container .section-header {
  padding: 16px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.camera-details-container .section-title {
  margin: 0;
  font-weight: 600;
  color: #3f4254;
  font-size: 16px;
  display: flex;
  align-items: center;
}

.camera-details-container .section-content {
  padding: 20px;
}

/* Detail Items */
.camera-details-container .detail-item {
  display: flex;
  flex-direction: column;
}

.camera-details-container .detail-label {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 6px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.camera-details-container .detail-value {
  font-size: 15px;
  color: #212529;
  font-weight: 500;
}

.camera-details-container .detail-value.highlight {
  color: #4361ee;
  font-weight: 600;
}

.camera-details-container .detail-value.code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  background-color: #f8f9fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 14px;
}

/* Status Badge */
.camera-details-container .status-badge {
  font-size: 12px;
  padding: 6px 12px;
  font-weight: 500;
}

/* Module Cards Styling */
.module-cards {
  margin-top: 10px;
}

.module-card {
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.module-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.module-card-header {
  padding: 15px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
}

.module-id {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.05);
  color: #6c757d;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.module-name {
  margin: 0;
  font-weight: 600;
  color: #3f4254;
  font-size: 16px;
  padding-right: 30px;
}

.module-card-body {
  padding: 15px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.time-schedule {
  display: flex;
  align-items: flex-start;
}

.schedule-icon {
  margin-right: 12px;
  padding-top: 2px;
}

.schedule-details {
  flex-grow: 1;
}

.schedule-label {
  display: block;
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 5px;
  font-weight: 500;
}

.time-range {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  font-weight: 600;
  color: #495057;
}

.start-time, .end-time {
  background-color: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 14px;
}

.time-separator {
  margin: 0 8px;
  color: #adb5bd;
  font-size: 12px;
}

/* Module card colors - add variety */
.module-card:nth-child(3n+1) .module-card-header {
  background: linear-gradient(135deg, #f0f7ff 0%, #e1effe 100%);
}

.module-card:nth-child(3n+2) .module-card-header {
  background: linear-gradient(135deg, #f0fff4 0%, #dcfce7 100%);
}

.module-card:nth-child(3n+3) .module-card-header {
  background: linear-gradient(135deg, #fff7ed 0%, #ffedd5 100%);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .module-card {
    margin-bottom: 15px;
  }
}

/* Empty State */
.camera-details-container .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  text-align: center;
}

.camera-details-container .empty-state p {
  margin-top: 10px;
  margin-bottom: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .camera-details-container .section-content {
    padding: 15px;
  }
  
  .camera-details-container .detail-item {
    margin-bottom: 15px;
  }
}


/* Add this CSS globally (e.g., in your styles.css or inside a <style> tag) */
.blinking-red-dot {
  height: 10px;
  width: 10px;
  background-color: red;
  border-radius: 50%;
  display: inline-block;
  animation: blink 1s infinite;
  margin-left: 10px;
}

@keyframes blink {
  0% { opacity: 1; }
  50% { opacity: 0.1; }
  100% { opacity: 1; }
}

.green-dot {
  height: 10px;
  width: 10px;
  background-color: green;
  border-radius: 50%;
  display: inline-block;
  margin-left: 10px;
}
