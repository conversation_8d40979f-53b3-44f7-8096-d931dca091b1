import React, { useContext, useEffect, useState } from 'react'
import { H1, H5, P } from '../../../../../AbstractElements'
import formDataContext from '../../../../../_helper/formData';
import { Row, Col, FormGroup, Label, Input, Button, Form, Alert, Container } from 'reactstrap';
import { useForm, Controller } from 'react-hook-form';
import { useNavigate, useLocation } from 'react-router-dom';
import axios from 'axios';
import { Typeahead } from 'react-bootstrap-typeahead';
import 'react-bootstrap-typeahead/css/Typeahead.css';
import AreaService from '../../../../../api/areaService';
import { errorToast, successToast } from '../../../../../_helper/helper';
import { DynamicInputField } from '../Components/DynamicInputField';
import { addFactoryLabelToAreas, handleFinalSubmission, addLabeFunction, GenerateAreaOptions, GenerateSub_AreaOptions, getFactoryAreasWithLabel, handleBasicInput, handleTypeaheadChange } from '../../HelpingFunctions/HelpingFunctions';
import { DynamicTypeaheadField } from '../Components/DynamicTypeAhead';
import DynamicNotificationBox from '../Components/DynamicNotificationBox';
import { BeatLoader } from 'react-spinners';

export default function AddUserForm() {
  const { AddUserData, setAddUserData } = useContext(formDataContext);
  const navigate = useNavigate();
  const location = useLocation();
  const isEditMode = location?.state?.type === 'edit';
  const [All_Factories, setAll_Factories] = useState([]);
  const [AllRoles, setAllRoles] = useState([]);
  const [loading, setloading] = useState(true);
  const [FactoryOptions, setFactoryOptions] = useState([]);
  const [errors, setErrors] = useState({
    name: '',
    email: '',
    role_id: '',
    factories: '',
    areas: '',
    mobile_no: '',
    password: '',
    email_notifications_areas: '',
    email_notifications_sub_areas: '',
    email_alert_emails: '',
    whatsapp_notifications_areas: '',
    whatsapp_notifications_sub_areas: '',
    whatsapp_phone_numbers: '',
    user_id: ''
  });
  const [FormData, setFormData] = useState({
    "id": '',
    "email": "",
    "name": "",
    "mobile_no": "",
    "role_name": "",
    "role_id": "",
    "status": false,
    "factories": [],
    "whatsapp_notifications": {
      "toggle": false,
      "areas": [],
      "sub_areas": [],
      "phone_numbers": []
    },
    "email_notifications": {
      "toggle": false,
      "areas": [],
      "sub_areas": [],
      "alert_emails": []
    },
    "areas": [],
    "password": ""
  })

  const fetchFactories = async () => {
    const roles = fetchRoless()
    try {
      const res = await AreaService.getAllFactoriesDataForIT();
      if (res.status == 200) {
        setAll_Factories(res.data.data)
        const filteredOptions = res.data.data.map((item) => ({
          factory_id: item.factory_id,
          factory_name: item.name
        }))
        setFactoryOptions(filteredOptions)
      }
    } catch (error) {
      errorToast('Error while fetching data')
    }
  }
  const fetchRoless = async () => {
    const role = JSON.parse(localStorage.getItem("role"))
    try {
      const roless = await AreaService.fetchAllRolesforIT();
      if (roless) {
        const roles = roless.data.data.map((item) => ({
          name: item.role_name,
          value: item.role_id
        }))
        setAllRoles(roles);
      }
    } catch (error) {
      errorToast('You are not authorized to add user');
      // navigate( `${process.env.PUBLIC_URL}/dashboard/default/${role}` )
    }
  }
  useEffect(() => {
    if (isEditMode && AddUserData && AddUserData.role_id == 8) {
      console.log(AddUserData)
      const updatedFormData = addFactoryLabelToAreas(All_Factories, AddUserData.areas);
      const upatedWhatsAppArea = addFactoryLabelToAreas(All_Factories, AddUserData.whatsapp_notifications[0].areas);
      const updatedEmailArea = addFactoryLabelToAreas(All_Factories, AddUserData.email_notifications[0].areas);
      const whatsapp = AddUserData.whatsapp_notifications[0] || {};
      const email = AddUserData.email_notifications[0] || {}
      const UpdatedData = {
        ...AddUserData,
        areas: updatedFormData,
        whatsapp_notifications: {
          ...whatsapp,
          areas: upatedWhatsAppArea
        },
        email_notifications: {
          ...email,
          areas: updatedEmailArea
        },
        ...(location?.state?.type === 'edit' ? {
          admin_id: JSON.parse(localStorage.getItem('userData'))?.id || 40,
          user_id: AddUserData.id,

        } : {
          user_id: JSON.parse(localStorage.getItem('userData'))?.id || 40,

        })
      }
      setFormData(UpdatedData)
    }
    else if (isEditMode && AddUserData.role_id != 8) {
      const updatedFormData = addFactoryLabelToAreas(All_Factories, AddUserData.areas);
      const whatsapp = AddUserData.whatsapp_notifications[0] || {};
      const email = AddUserData.email_notifications[0] || {}
      const UpdatedData = {
        ...AddUserData,
        areas: updatedFormData,
        whatsapp_notifications: {
          ...whatsapp,
          areas: GenerateAreaOptions(All_Factories, AddUserData.factories?.map(item => item.factory_id))
        },
        email_notifications: {
          ...email,
          areas: GenerateAreaOptions(All_Factories, AddUserData.factories?.map(item => item.factory_id))
        },
        ...(location?.state?.type === 'edit' ? {
          admin_id: JSON.parse(localStorage.getItem('userData'))?.id || 40,
          user_id: AddUserData.id,

        } : {
          user_id: JSON.parse(localStorage.getItem('userData'))?.id || 40,

        })
      }
      setFormData(UpdatedData)
    }
    else {
      setFormData({
        ...FormData,
        user_id: JSON.parse(localStorage.getItem('userData'))?.id || 40,
      })
    }
  }, [AddUserData, isEditMode, All_Factories])


  useEffect(() => {
    Promise.all([fetchFactories(), fetchRoless()]).then((e) => {
      setloading(false)
    })
  }, [])

  const handleCheckBox = (e, setFormData, field) => {
    if (!FormData.factories || FormData.factories.length === 0) {
      return errorToast("Please select a factory first");
    } else if (FormData.areas.length === 0 && FormData.role_id == 8) {
      return errorToast("Please select an area first");
    }

    let notification = FormData[field];
    if (Array.isArray(notification)) {
      notification = notification[0] || {};
    } else if (!notification) {
      notification = {};
    }

    if (FormData.role_id == 8) {
      const updatedField = {
        ...notification,
        toggle: e.target.checked,
      };

      setFormData({
        ...FormData,
        [field]: updatedField,
      });
    } else {
      const updatedField = {
        ...notification,
        toggle: e.target.checked,
        areas: GenerateAreaOptions(All_Factories, FormData.factories?.map(item => item.factory_id)),
      };

      setFormData({
        ...FormData,
        [field]: updatedField,
      });
    }

    // Clear notification errors when toggling
    if (field === 'email_notifications') {
      setErrors({
        ...errors,
        email_notifications_areas: '',
        email_notifications_sub_areas: '',
        email_alert_emails: ''
      });
    } else if (field === 'whatsapp_notifications') {
      setErrors({
        ...errors,
        whatsapp_notifications_areas: '',
        whatsapp_notifications_sub_areas: '',
        whatsapp_phone_numbers: ''
      });
    }
  };

  const validateEmail = (email) => {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailPattern.test(email);
  };

  const validatePassword = (password) => {
    const passwordPattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    return passwordPattern.test(password);
  };


  const validatePhone = (phone) => {
    const phonePattern = /^[0-9]{4}-[0-9]{7}$/;
    return phonePattern.test(phone);
  };


  const validateForm = () => {
    let isValid = true;
    let newErrors = {
      name: '',
      email: '',
      role_id: '',
      factories: '',
      areas: '',
      mobile_no: '',
      password: '',
      email_notifications_areas: '',
      email_notifications_sub_areas: '',
      email_alert_emails: '',
      whatsapp_notifications_areas: '',
      whatsapp_notifications_sub_areas: '',
      whatsapp_phone_numbers: ''
    };

    // Name validation
    if (!FormData.name.trim()) {
      newErrors.name = 'Name is required';
      isValid = false;
    }

    // Email validation
    if (!FormData.email.trim()) {
      newErrors.email = 'Email is required';
      isValid = false;
    } else if (!validateEmail(FormData.email)) {
      newErrors.email = 'Please enter a valid email';
      isValid = false;
    }

    // Role validation
    if (!FormData.role_id) {
      newErrors.role_id = 'Role is required';
      isValid = false;
    }

    // Factories validation
    if (FormData.factories.length === 0) {
      newErrors.factories = 'Please select at least one factory';
      isValid = false;
    }

    // Areas validation for role_id 8
    if (FormData.role_id == 8 && FormData.factories.length > 0 && FormData.areas.length === 0) {
      newErrors.areas = 'Please select at least one area';
      isValid = false;
    }

    // Phone validation
    if (!FormData.mobile_no.trim()) {
      newErrors.mobile_no = 'Phone number is required';
      isValid = false;
    } else if (!validatePhone(FormData.mobile_no)) {
      newErrors.mobile_no = 'Phone number should be in the format 0000-0000000.';
      isValid = false;
    }

    // Password validation (only for new users)
    if (!isEditMode && !FormData.password) {
      newErrors.password = 'Password is required';
      isValid = false;
    } else if (!isEditMode && !validatePassword(FormData.password)) {
      newErrors.password = 'Password must have at least 8 characters, one uppercase, one lowercase, one number and one special character';
      isValid = false;
    }

    // Email notifications validation
    if (FormData.email_notifications.toggle) {
      if (FormData.email_notifications.areas.length === 0) {
        newErrors.email_notifications_areas = 'Please select at least one area for email notifications';
        isValid = false;
      }

      if (FormData.role_id == 8 && FormData.email_notifications.sub_areas?.length === 0) {
        newErrors.email_notifications_sub_areas = 'Please select at least one sub area for email notifications';
        isValid = false;
      }

      if (FormData.email_notifications.alert_emails?.length === 0) {
        newErrors.email_alert_emails = 'Please add at least one email address';
        isValid = false;
      } else {
        // Validate each email
        const invalidEmails = FormData.email_notifications.alert_emails.filter(email => !validateEmail(email));
        if (invalidEmails.length > 0) {
          newErrors.email_alert_emails = 'Please enter valid email addresses';
          isValid = false;
        }
      }
    }

    // WhatsApp notifications validation
    if (FormData.whatsapp_notifications.toggle) {
      if (FormData.whatsapp_notifications.areas.length === 0) {
        newErrors.whatsapp_notifications_areas = 'Please select at least one area for WhatsApp notifications';
        isValid = false;
      }

      if (FormData.role_id == 8 && FormData.whatsapp_notifications.sub_areas?.length === 0) {
        newErrors.whatsapp_notifications_sub_areas = 'Please select at least one sub area for WhatsApp notifications';
        isValid = false;
      }

      if (FormData.whatsapp_notifications.phone_numbers?.length === 0) {
        newErrors.whatsapp_phone_numbers = 'Please add at least one phone number';
        isValid = false;
      } else {
        // Validate each phone number
        const invalidPhones = FormData.whatsapp_notifications.phone_numbers.filter(phone => !validatePhone(phone));
        if (invalidPhones.length > 0) {
          newErrors.whatsapp_phone_numbers = 'Phone number should be in the format 0000-0000000.';
          isValid = false;
        }
      }
    }

    setErrors(newErrors);
    return isValid;
  };
 

  const handleInputWithValidation = (e, setFormData) => {
    const { name, value } = e.target;
    handleBasicInput(e, setFormData);

    // Clear error when user starts typing
    setErrors({
      ...errors,
      [name]: ''
    });
  }
  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) { 
      const GeneratePayload = handleFinalSubmission(FormData, All_Factories, isEditMode);
        setloading(true)
        if (isEditMode) {
          handleEditApi(GeneratePayload);
        } else {
          addUser(GeneratePayload);
        }
 
    } else {
      errorToast("Please fix the errors in the form");
    }
  }
  const handleEditApi = async (payload) => {
    AreaService.updateUser(payload).then((res) => {
      if (res.status == 200) {
        successToast('User updated');
        const role = JSON.parse(localStorage.getItem('role'));
        navigate(`${process.env.PUBLIC_URL}/dashboard/default/${role}`);
        setloading(false)
        return
      }
    }).catch((err) => {
      errorToast('error')
      setloading(false)
    })
  }
  const addUser=async(payload)=>{
    AreaService.AdduserIT(payload).then((res) => {
      if (res.status == 200) {
        successToast('User added');
        const role = JSON.parse(localStorage.getItem('role'));
        const url = `${process.env.PUBLIC_URL}/dashboard/default/${role}`;
        navigate(url, { state: { addUser: true } });
        setloading(false)
        return
      }
    }).catch((err) => {
      errorToast('error')
      setloading(false)
    }) 
  }

  return (
    <Container fluid={true}>
      <br />
      <Form>
        <H5>{isEditMode ? "Edit" : "Add"} User</H5>
        {loading ? <div className='d-flex justify-content-center align-items-center flex-column' style={{height:"50vh"}}>
          <p>Please wait...</p>
          <BeatLoader />
        </div>: 
        <Row className='mt-2'>
          <Col sm="12" md="6" >
            <DynamicInputField
              label="Name"
              type="text"
              name="name"
              value={FormData.name}
              onChange={(e) => handleInputWithValidation(e, setFormData)}
              error={errors.name}
            />
            <DynamicInputField
              label="Email"
              type="email"
              name="email"
              value={FormData.email}
              onChange={(e) => handleInputWithValidation(e, setFormData)}
              error={errors.email}
            />
            <DynamicInputField
              label="Role"
              type="role_id"
              name="role_id"
              value={FormData.role_id}
              onChange={(e) => {
                handleInputWithValidation(e, setFormData);
                if (e.target.value == 8) {
                  setFormData((prev) => ({
                    ...prev,
                    factories: [],
                    areas: [],
                    whatsapp_notifications: {
                      ...prev.whatsapp_notifications,
                      areas: [],
                      sub_areas: [],
                      toggle: false
                    },
                    email_notifications: {
                      ...prev.email_notifications,
                      areas: [],
                      sub_areas: [],
                      toggle: false
                    },
                  }));
                }
                else {
                  setFormData((prev) => ({
                    ...prev,
                    areas: GenerateAreaOptions(All_Factories, FormData.factories?.map(item => item.factory_id)),
                    whatsapp_notifications: {
                      ...prev.whatsapp_notifications,
                      areas: GenerateAreaOptions(All_Factories, FormData.factories?.map(item => item.factory_id)),
                      sub_areas: GenerateSub_AreaOptions(All_Factories, FormData.factories?.map(item => item.factory_id), FormData.factories?.map(item => item.factory_id)),
                      toggle: false
                    },
                    email_notifications: {
                      ...prev.email_notifications,
                      areas: GenerateAreaOptions(All_Factories, FormData.factories?.map(item => item.factory_id)),
                      sub_areas: GenerateSub_AreaOptions(All_Factories, FormData.factories?.map(item => item.factory_id), FormData.factories?.map(item => item.factory_id)),
                      toggle: false
                    },
                  }));
                }
                // Clear factories and areas errors when role changes
                setErrors({
                  ...errors,
                  role_id: '',
                  factories: '',
                  areas: ''
                });
              }}
              isDropdown={true}
              options={AllRoles || []}
              error={errors.role_id}
            />
            <DynamicTypeaheadField
              label="Factories"
              id="factories"
              options={FactoryOptions || []}
              selected={FormData.factories}
              labelKey='factory_name'
              multiple={true}
              placeholder="Select Factories"
              onChange={(e) => {
                handleTypeaheadChange(FormData, setFormData, 'factories')(e);
                setFormData((prev) => ({
                  ...prev,
                  areas: [],
                  whatsapp_notifications: {
                    ...prev.whatsapp_notifications,
                    areas: [],
                    sub_areas: [],
                    toggle: false
                  },
                  email_notifications: {
                    ...prev.email_notifications,
                    areas: [],
                    sub_areas: [],
                    toggle: false
                  },
                }));
                // Clear factories error when selection changes
                setErrors({
                  ...errors,
                  factories: '',
                  areas: ''
                });
              }}
              miniAlert={FormData.factories?.length > 0 && FormData.role_id != 8 ? true : false}
              miniAlertMsg='All areas for the selected factory have been automatically selected.'
              error={errors.factories}
            />
            {FormData.role_id == 8 && FormData.factories?.length > 0 &&
              <DynamicTypeaheadField
                label="Area"
                id="areas"
                options={GenerateAreaOptions(All_Factories, FormData.factories?.map(item => item.factory_id)) || []}
                selected={FormData.areas}
                labelKey='label'
                multiple={true}
                placeholder="Select Areas"
                onChange={(e) => {
                  handleTypeaheadChange(FormData, setFormData, 'areas')(e);
                  // Clear areas error when selection changes
                  setErrors({
                    ...errors,
                    areas: ''
                  });
                }}
                error={errors.areas}
              />}
            <DynamicInputField
              label="Phone"
              type="text"
              name="mobile_no"
              value={FormData.mobile_no}
              onChange={(e) => handleInputWithValidation(e, setFormData)}
              error={errors.mobile_no}
            />
            {isEditMode == false &&
              <DynamicInputField
                showPassMsg={true}
                label="Password"
                type="password"
                name="password"
                value={FormData.password}
                onChange={(e) => handleInputWithValidation(e, setFormData)}
                error={errors.password}
              />}
          </Col>
          <Col sm="12" md="6" >
            <DynamicNotificationBox
              All_Factories={All_Factories}
              AllFactoryIds={FormData.factories?.map(item => item.factory_id)}
              Data={FormData.email_notifications}
              setFormData={setFormData}
              FormData={FormData}
              AreaOptions={GenerateAreaOptions(All_Factories, FormData.factories?.map(item => item.factory_id)) || []}
              label="Email Notification"
              type="checkbox"
              name="email_notifications"
              checked={FormData?.email_notifications?.toggle}
              onChange={(e) => handleCheckBox(e, setFormData, 'email_notifications')}
              errors={{
                email_notifications_areas: errors.email_notifications_areas,
                email_notifications_sub_areas: errors.email_notifications_sub_areas,
                email_alert_emails: errors.email_alert_emails
              }}
            />
            <DynamicNotificationBox
              flag={true}
              All_Factories={All_Factories}
              AllFactoryIds={FormData.factories?.map(item => item.factory_id)}
              Data={FormData.whatsapp_notifications}
              setFormData={setFormData}
              FormData={FormData}
              AreaOptions={GenerateAreaOptions(All_Factories, FormData.factories?.map(item => item.factory_id)) || []}
              label="Whatsapp Notification"
              type="checkbox"
              name="whatsapp_notifications"
              checked={FormData?.whatsapp_notifications?.toggle}
              onChange={(e) => handleCheckBox(e, setFormData, 'whatsapp_notifications')}
              errors={{
                whatsapp_notifications_areas: errors.whatsapp_notifications_areas,
                whatsapp_notifications_sub_areas: errors.whatsapp_notifications_sub_areas,
                whatsapp_phone_numbers: errors.whatsapp_phone_numbers
              }}
            />
          </Col>
          <Col className='d-flex justify-content-end gap-2 mt-3' >
            <Button color='primary' type='submit' onClick={handleSubmit}>
              {isEditMode ? 'Edit User' : 'Add User'}
            </Button>
            <Button color='dark' type='button' onClick={() => navigate(-1)}>
              Cancel
            </Button>
          </Col>
        </Row> }
      </Form>
    </Container>
  );
}
