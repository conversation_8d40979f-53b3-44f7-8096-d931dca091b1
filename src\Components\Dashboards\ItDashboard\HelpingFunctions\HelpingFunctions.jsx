import { errorToast } from "../../../../_helper/helper";
import { factories } from "../../../../Data/staticData/data";

export function handleBasicInput(e,setFormData){
  const {name,value} = e.target 
    setFormData((prev) => ({
        ...prev,
        [name]: value
    }))
  }

export const handleTypeaheadChange = (formData, setFormData, field) => (selected) => {
 if( !formData.role_id ){
  return errorToast("Please select a role first");
 }
  else  if (formData.role_id == 8 && selected && selected.length > 1) {
    return errorToast("You can not select more than one factory or area for this user");
  }

  const updatedFormData = {
    ...formData,
    [field]: selected || [],
  };

  // Agar role 8 hai aur factories update ho rahi hain, to areas ko reset kar do
  if (formData.role_id == 8 && field == 'factories') {
    updatedFormData.areas = [];
    updatedFormData.whatsapp_notifications = {
      ...formData.whatsapp_notifications,
      areas: [],
      sub_areas: [],
      toggle: false,
    };
    updatedFormData.email_notifications = {
      ...formData.email_notifications,
      areas: [],
      sub_areas: [],
      toggle: false,
    };
  }
  else if(formData.role_id == 8 && field == 'areas')
  {
    updatedFormData.whatsapp_notifications = {
      ...formData.whatsapp_notifications,
      areas: selected,
      sub_areas: [],
      toggle: false,
    };
    updatedFormData.email_notifications = {
      ...formData.email_notifications,
      areas: selected,
      sub_areas: [],
      toggle: false,
    };
  }

  setFormData(updatedFormData);
};



export function GenerateAreaOptions(factories, selectedFactoryIds = [], flag = false) {
  // Agar selectedFactoryIds empty ho, to sab factories consider karenge
  const filteredFactories = selectedFactoryIds.length > 0 
    ? factories.filter(factory => selectedFactoryIds.includes(factory.factory_id))
    : factories;

  if (flag) {
    // Sirf area ids ka array return karein
    return filteredFactories
      .flatMap(factory => factory.areas.map(area => area.id));
  }

  // Warna purana object structure return karein
  const areaOptions = filteredFactories
    .flatMap(factory =>
      factory.areas.map(area => ({
        area_id: area.id,
        area_name: area.area,
        label: `${area.area} - ${factory.name}`
      }))
    );

  return areaOptions;
}



export function getFactoryAreasWithLabel(factories, factoryId) {
  // Find the factory by id
  const factory = factories.find(f => f.factory_id === factoryId);

  // If factory not found or no areas, return empty array
  if (!factory || !factory.areas) {
    return [];
  }

  // Map over areas and add 'label' field as "Area Name - Factory Name"
  return factory.areas.map(area => ({
    ...area,
    label: `${area.area} - ${factory.name}`,
  }));
}

export function addFactoryLabelToAreas(factories, areas) {
  return areas?.map(areaObj => {
    // For each area, find the factory that contains this area_id
    let factoryName = null;

    for (const factory of factories) {
      if (factory.areas && factory.areas.some(area => area.id === areaObj.area_id)) {
        factoryName = factory.name;
        break;
      }
    }

    return {
      ...areaObj,
      label: factoryName ? `${areaObj.area_name} - ${factoryName}` : areaObj.area_name,
    };
  });
}


export function GenerateSub_AreaOptions(factories, selectedAreaIds = [], selectedFactoryIds = []) {
  // Filter factories agar selectedFactoryIds diye gaye hain
  const filteredFactories = selectedFactoryIds.length > 0
    ? factories.filter(factory => selectedFactoryIds.includes(factory.factory_id))
    : factories;

  // Sab sub areas collect karenge filtered factories ke areas me se, par sirf un areas ke jinke id selectedAreaIds me hain
  const subAreaOptions = [];

  filteredFactories.forEach(factory => {
    factory.areas.forEach(area => {
      if (selectedAreaIds.includes(area.id)) {
        // area.sub_area ek array hai jisme objects with id and name hote hain
        area.sub_area.forEach(subArea => {
          subAreaOptions.push({
            sub_area_id: subArea.id,
            sub_area_name: subArea.name,
            // area_id: area.id,
            // area_name: area.area,
            // factory_id: factory.factory_id,
            // factory_name: factory.name,
            // label: `${subArea.name} - ${area.area} - ${factory.name}`
          });
        });
      }
    });
  });

  return subAreaOptions;
}


export function errorChecking(FormData){

}
function convertIdArrays(obj) {
  // Agar obj array hai
  if (Array.isArray(obj)) {
    // Check if elements me id-type keys hain
    if (obj.length > 0 && typeof obj[0] === 'object' && obj[0] !== null) {
      // Find id key in first element, e.g., keys ending with _id or exactly id
      const idKey = Object.keys(obj[0]).find(k => k === 'id' || k.endsWith('_id'));

      if (idKey) {
        // Return array of those id values
        return obj.map(item => item[idKey].toString());
      } else {
        // Agar id key nahi mili, toh har element recursively check karo
        return obj.map(item => convertIdArrays(item));
      }
    } else {
      // Agar simple array hai jisme objects nahi, toh return as is
      return obj;
    }
  } 
  // Agar object hai toh uske har key ko check karo recursively
  else if (typeof obj === 'object' && obj !== null) {
    const newObj = {};
    for (const key in obj) {
      newObj[key] = convertIdArrays(obj[key]);
    }
    return newObj;
  } 
  // Agar primitive value ho toh waise hi return karo
  else {
    return obj;
  }
}

function getSubAreaIds(factories, areaIds) {
  const result = [];

  for (const factory of factories) {
    for (const area of factory.areas || []) {
      if (areaIds.includes(area.id)) {
        const subIds = area.sub_area.map(sub => sub.id);
        result.push(...subIds);
      }
    }
  }

  return result;
}
 
export function handleFinalSubmission(FormData, All_Factories,isEditMode) { 
  const factoryIds = FormData.factories?.map(item => item.factory_id) || [];
 
  const areas = (FormData.role_id != 8)
    ? GenerateAreaOptions(All_Factories, factoryIds, true)
    : FormData.areas?.map(item => item.area_id) || [];
 
  const getNotificationAreaIds = (notification) => notification?.areas?.map(item => item.area_id) || [];

  const whatsapp_areas = getNotificationAreaIds(FormData.whatsapp_notifications);
  const email_areas = getNotificationAreaIds(FormData.email_notifications);

  const finalpayload = {
    ...FormData,
    areas,
    whatsapp_notifications: {
      ...FormData.whatsapp_notifications,
      areas: whatsapp_areas,
      sub_areas: (FormData.role_id != 8) 
        ? getSubAreaIds(All_Factories, whatsapp_areas) 
        : (FormData.whatsapp_notifications?.sub_areas || []).map(sa => sa.sub_area_id || sa) 
    },
    email_notifications: {
      ...FormData.email_notifications,
      areas: email_areas,
      sub_areas: (FormData.role_id != 8) 
        ? getSubAreaIds(All_Factories, email_areas) 
        : (FormData.email_notifications?.sub_areas || []).map(sa => sa.sub_area_id || sa)
    },
    factories: factoryIds
  };

 return finalpayload
   
}

