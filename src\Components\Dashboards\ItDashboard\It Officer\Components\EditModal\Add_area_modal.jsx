import React, { useRef, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>er, ModalBody, ModalFooter, Button, Form, FormGroup, Label, Input, Row, Col, FormFeedback } from 'reactstrap';

const Add_area_modal = ({
  modalOpen,
  setModalOpen,
  form,
  setForm,
  handleInputChange,
  handleAddArea,
  formLoading,
  factories,
  type,
  handleupdateAreasubmission,
}) => {
  const fileInputRef = useRef();
  const [errors, setErrors] = useState({});

  const handleLogoChange = (e) => {
    const file = e.target.files[0];
    setForm({ ...form, logo: file });
    setErrors(prev => ({ ...prev, logo: undefined }));
  };

  const validate = () => {
    const newErrors = {};
    if (!form.factory_id) newErrors.factory_id = 'Factory is required';
    if (!form.name || !form.name.trim()) newErrors.name = 'Area Name is required';
    if (!form.address || !form.address.trim()) newErrors.address = 'Area Address is required';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const onSubmit = (e) => {
    e.preventDefault();
    if (!validate()) return;
    if(type == 'edit') return handleupdateAreasubmission(e);
    else if(type == 'add') return handleAddArea(e); 
  };

  return (
    <Modal isOpen={modalOpen} toggle={() => setModalOpen(false)} centered>
      <ModalHeader toggle={() => setModalOpen(false)}>{type === 'add' ? 'Add' : 'Edit'} Area</ModalHeader>
      <Form>
        <ModalBody>
          <Row>
            <Col md={12}>
              <FormGroup>
                <Label for="factory_id">Factory</Label>
                <Input
                  type="select"
                  name="factory_id"
                  id="factory_id"
                  value={form.factory_id}
                  onChange={handleInputChange}
                  required
                  invalid={!!errors.factory_id}
                >
                  <option value="">Select Factory</option>
                  {factories.map(fac => (
                    <option key={fac.factory_id} value={fac.factory_id}>
                      {fac.name}
                    </option>
                  ))}
                </Input>
                {errors.factory_id && <FormFeedback>{errors.factory_id}</FormFeedback>}
              </FormGroup>
            </Col>
          </Row>
          <FormGroup>
            <Label for="name">Area Name</Label>
            <Input
              type="text"
              name="name"
              id="name"
              value={form.name}
              onChange={handleInputChange}
              required
              placeholder="Enter Area Name"
              invalid={!!errors.name}
            />
            {errors.name && <FormFeedback>{errors.name}</FormFeedback>}
          </FormGroup>
          <FormGroup>
            <Label for="address">Area Address</Label>
            <Input
              type="text"
              name="address"
              id="address"
              value={form.address}
              onChange={handleInputChange}
              required
              placeholder="Enter Area Address"
              invalid={!!errors.address}
            />
            {errors.address && <FormFeedback>{errors.address}</FormFeedback>}
          </FormGroup>
          <FormGroup>
            <Label for="logo">Area Logo</Label>
            <Input
              type="file"
              name="logo"
              id="logo"
              accept="image/*"
              innerRef={fileInputRef}
              onChange={handleLogoChange}
            />
            {form.logo instanceof File && (
              <div className="mt-2 text-center">
                <img
                  src={URL.createObjectURL(form.logo)}
                  alt="Preview"
                  className="img-thumbnail"
                  style={{ maxWidth: '120px', maxHeight: '120px' }}
                />
              </div>
            )}
          </FormGroup>
        </ModalBody>
        <ModalFooter>
          <Button color="primary" type="submit" disabled={formLoading} onClick={(e) => (onSubmit(e))}>
            {formLoading ? 'Adding...' : type === 'add' ? 'Add Area' : 'Update Area'}
          </Button>
          <Button color="secondary" onClick={() => setModalOpen(false)}>
            Cancel
          </Button>
        </ModalFooter>
      </Form>
    </Modal>
  );
};

export default Add_area_modal;
