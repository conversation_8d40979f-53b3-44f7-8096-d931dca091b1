import React, { useEffect, useState } from 'react';
import {
  Con<PERSON>er,
  Button,
  FormGroup,
  Label,
  Input,
  Spinner,
  Badge,
  Card,
  CardHeader,
  CardBody,
  Row,
  Col,
  Media
} from 'reactstrap';
import DataTable from 'react-data-table-component';
import ItService from '../../../../../api/itService';
import FactoryService from '../../../../../api/factoryService';
import Add_area_modal from '../Components/EditModal/Add_area_modal';
import { errorToast, successToast } from '../../../../../_helper/helper';
import { FiPlus, FiHome, FiUser, FiClock, FiCheckCircle, FiXCircle, FiMap, FiFilter, FiLayers } from 'react-icons/fi';
import styled from 'styled-components';
import ConfirmationModal from '../Components/ConifrmationModal/Confirmation_Modal';
import AreaService from '../../../../../api/areaService';
import { StatsCard, StatsContent, StatsIcon, StatsTitle, StatsValue, StyledSelect } from './styledCards';
import { MdOutlineDashboard } from 'react-icons/md';

// Styled Components
const PrimaryButton = styled(Button)`
  background-color: #1e67d5;
  border: none;
  border-radius: 8px;
  padding: 0.625rem 1.5rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(30, 103, 213, 0.2);
  font-size: 0.875rem;
  
  &:hover {
    background-color: #1654b1;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(30, 103, 213, 0.3);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &:disabled {
    background-color: #a0b9e0;
    cursor: not-allowed;
  }
`;

const StatusBadge = styled(Badge)`
  padding: 0.375rem 0.75rem;
  font-weight: 500;
  font-size: 0.75rem;
  border-radius: 12px;
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  background-color: ${props => props.active ? 'rgba(40, 199, 111, 0.12)' : 'rgba(234, 84, 85, 0.12)'} !important;
  color: ${props => props.active ? '#28c76f' : '#ea5455'} !important;
`;

const EmptyStateContainer = styled.div`
  height: 300px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 2rem;
  background-color: #f8f8f8;
  border-radius: 12px;
  margin: 1rem 0;
`;

const EmptyStateIcon = styled.div`
  font-size: 3rem;
  color: #b8c2cc;
  margin-bottom: 1rem;
`;


const StyledCard = styled(Card)`
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(34, 41, 47, 0.08);
  border: none;
  overflow: hidden;
  margin-bottom: 2rem;
`;
const FilterInput = styled(Input)`
  max-width: 250px;
  border-radius: 8px;
  border: 1px solid #dfe3e7;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  &:focus {
    border-color: #1e67d5;
    box-shadow: 0 0 0 0.2rem rgba(30, 103, 213, 0.1);
  }
`;

export default function AddArea() {
  const [areas, setAreas] = useState([]);
  const [subAreas, setSubAreas] = useState([])
  const [loading, setLoading] = useState(true);
  const [modalOpen, setModalOpen] = useState(false);
  const [factories, setFactories] = useState([]);
  const [form, setForm] = useState({
    user_id: JSON.parse(localStorage.getItem('userData'))?.id || 0,
    factory_id: '',
    name: '',
    address: '',
  });
  const [selectedFactory, setSelectedFactory] = useState(null)
  const [formLoading, setFormLoading] = useState(false);
  const [filterText, setFilterText] = useState('');
  const [showconfirmationModal, setshowconfirmationModal] = useState(false)
  const taggle_confirmation_modal = () => setshowconfirmationModal(!showconfirmationModal)
  const [confirmation_modal_data, setconfirmation_modal_data] = useState();
  const [modalType, setmodalType] = useState('add')
  let filteredItems = areas.filter(item =>
    item.area_name.toLowerCase().includes(filterText.toLowerCase()) ||
    item.factory.toLowerCase().includes(filterText.toLowerCase()) ||
    (item.area_owner && item.area_owner.toLowerCase().includes(filterText.toLowerCase()))
  );
  const [filteredItemState, setFilteredItemsState] = useState(filteredItems)

  const tableColumns = [
    {
      name: 'FACTORY',
      selector: row => row.factory,
      sortable: true,
      cell: row => (
        <div className="d-flex align-items-center">
          {/* <FiHome className="mr-2" size={16} color="#5e5873" /> */}
          <span className="font-weight-medium">{row.factory}</span>
        </div>
      ),
    },
    {
      name: 'AREA',
      selector: row => row.area_name,
      sortable: true,
      cell: row => (
        <div className="d-flex align-items-center">
          {/* <FiMap className="mr-2" size={16} color="#5e5873" /> */}
          <span className="font-weight-medium">{row.area_name}</span>
        </div>
      ),
    },
    {
      name: 'OWNER',
      selector: row => row.area_owner || 'N/A',
      sortable: true,
      cell: row => (
        <div className="d-flex align-items-center">
          <FiUser className="mr-2" size={16} color="#5e5873" />
          <span>{row.area_owner || 'N/A'}</span>
        </div>
      ),
      minWidth: '180px'
    },
    {
      name: 'STATUS',
      selector: row => row.active,
      sortable: true,
      cell: row => (
        <div className="d-flex align-items-center gap-2">
          <StatusBadge active={row.active} pill>
            {row.active ? (
              <>
                <FiCheckCircle size={14} />
                <span>ACTIVE</span>
              </>
            ) : (
              <>
                <FiXCircle size={14} />
                <span>INACTIVE</span>
              </>
            )}
          </StatusBadge>
        </div>
      ),
    },
    {
      name: 'ACTION',
      cell: row => (
        <div className="d-flex align-items-center gap-2">
          <Media body className={`text-end switch-size d-flex justify-content-center align-items-center  `} style={{ width: '100%' }}>
            <Label className="switch mt-2" style={{ marginLeft: '15px' }}>
              <Input
                type="checkbox"
                checked={row.active}
                onChange={() => { setconfirmation_modal_data(row); taggle_confirmation_modal() }}
              />
              <span className={`switch-state`} style={{ height: '25px', width: '45px' }}></span>
            </Label>
          </Media>
        </div>
      ),
    },
    {
      name: 'Update',
      sortable: false,
      cell: row => (
        <div className="d-flex align-items-center">
          <Button color="primary"
            onClick={() => handleAreaUpdate(row)}
          >
            Update {row.area_name}
          </Button>
        </div>
      ),
    },
    {
      name: 'CREATED DATE',
      selector: row => row.created_at,
      sortable: true,
      cell: row => (
        <div className="d-flex align-items-center">
          <FiClock className="mr-2" size={16} color="#5e5873" />
          <span>{new Date(row.created_at).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
          })}</span>
        </div>
      ),
    },

  ];

  async function fetchFactories() {
    try {
      const res = await FactoryService.getAllFactories();
      setFactories(res.data?.data || []);
      console.log('res.data?.data11', res.data?.data)
    } catch (error) {
      errorToast('Failed to load factories');
    }
  }

  async function fetchAreas() {
    setLoading(true);
    try {
      const res = await ItService.getallareas();
      console.log(res.data?.data)
      setAreas(res.data?.data || []);
      console.log('res.data?.data', res.data?.data)
    } catch (error) {
      errorToast('Failed to load areas');
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    Promise.all([fetchFactories(), fetchAreas()])
      .catch(error => {
        errorToast('Error while fetching data');
        setLoading(false);
      });
  }, []);

  const handleInputChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleAddArea = async (e) => {
    e.preventDefault();
    setFormLoading(true);

    try {
      const formData = new FormData();
      formData.append('user_id', form.user_id);
      formData.append('factory_id', form.factory_id);
      formData.append('name', form.name);
      formData.append('address', form.address);

      if (form.logo instanceof File) {
        formData.append('file', form.logo);
      }

      await ItService.addArea(formData);
      successToast('Area added successfully');
      setModalOpen(false);
      setForm({
        user_id: JSON.parse(localStorage.getItem('userData'))?.id || 0,
        factory_id: '',
        name: '',
        address: '',
        logo: null
      });
      fetchAreas();
    } catch (error) {
      errorToast('Error while adding area');
    } finally {
      setFormLoading(false);
    }
  };

  const handleStatusToggle = () => {
    AreaService.update_area_status(confirmation_modal_data?.area_id).then((res) => {
      if (res.status == 200) {
        successToast('Status updated');
        fetchAreas();
        taggle_confirmation_modal();
      }
    }).catch((err) => {
      errorToast('Error while updating status');
      taggle_confirmation_modal();
    })
  }

  const handleAreaUpdate = (row) => {
    setForm({
      user_id: JSON.parse(localStorage.getItem('userData'))?.id || 0,
      factory_id: row.factory_id,
      name: row.area_name,
      address: row.address,
      logo: null,
      area_id: row.area_id,
    });
    setmodalType('edit')
    setModalOpen(true);
  }

  const handleupdateAreasubmission = async (e) => {
    e.preventDefault();
    setFormLoading(true);
    try {
      const formData = new FormData();
      formData.append('user_id', form.user_id);
      formData.append('factory_id', form.factory_id);
      formData.append('area_id', form.area_id);
      formData.append('name', form.name);
      formData.append('address', form.address);
      if (form.logo instanceof File) {
        formData.append('file', form.logo);
      }
      const res = await ItService.updateArea(formData);
      if (res.status == 200) {
        successToast('Area updated successfully');
        setModalOpen(false);
        setForm({
          user_id: JSON.parse(localStorage.getItem('userData'))?.id || 0,
          factory_id: '',
          name: '',
          address: '',
          logo: null,
          area_id: '',
        });
        setFormLoading(false);
        setmodalType('add')
        fetchAreas();
      }
    } catch (error) {
      console.log(error)
      errorToast('Error while updating area');
    }
  }
   const handleFactoryChange = (e) => {
    const factoryId = e.target.value
    const factoryObj = factories.find(f => f.factory_id == factoryId) 
    setSelectedFactory(factoryObj)
    if (factoryObj && Array.isArray(factoryObj.areas)) { 
      const subAreaList = factoryObj.areas.flatMap(area =>
        (area.sub_area || []).map(sub => ({
          area_name: area.area,
          area_id: area.id,
          factory_name: factoryObj.name,
          factory_id: factoryObj.factory_id,
          sub_area_id: sub.id,
          sub_area_name: sub.name,
          sub_area_status:sub.active
        }))
      ); 
      console.log('areasssssss,', areas)
      const updateAreas = areas.filter(item => item.factory_id == factoryId);
      
      setFilteredItemsState(updateAreas)
      console.log('areasss', updateAreas)
      setSubAreas(subAreaList); 
    } else {
      setSubAreas([]);
    }
  }

  return (
    <Container fluid >
      <br />
      <Row className="mb-3">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <h4 className="mb-0">
              Area Management
            </h4>

            <div className='d-flex gap-2 flex-column-reverse'>
              <Button style={{height:'min-content'}} disabled={loading} color='primary' onClick={() => {
                setModalOpen(true); setmodalType('add'); setForm({
                  user_id: JSON.parse(localStorage.getItem('userData'))?.id || 0,
                  factory_id: '',
                  name: '',
                  address: '',
                  logo: null,
                  area_id: '',
                })
              }}>
                {/* <FiPlus size={18} /> */}
                Add New Area
              </Button>
              <FormGroup className="mb-0" style={{ minWidth: '250px' }}>
                <Label for="factorySelect" className="form-label">Select Factory</Label>
                <StyledSelect
                  type="select"
                  id="factorySelect"
                  value={selectedFactory?.factory_id || ''}
                  onChange={handleFactoryChange}
                  disabled={loading}
                >
                  <option value="">{loading ? 'Loading factories...' : 'Select Factory'}</option>
                  {factories.map((factory) => (
                    <option key={factory.factory_id} value={factory.factory_id}>
                      {factory.name}
                    </option>
                  ))}
                </StyledSelect>
              </FormGroup>
            </div>
           
          </div>
        </Col>
      </Row>
         {selectedFactory && (
                    <div className="row mb-4">
                      <div className="col-md-4">
                        <StatsCard>
                          <StatsIcon color="#7367f0">
                            <FiHome />
                          </StatsIcon>
                          <StatsContent>
                            <StatsTitle>Selected Factory</StatsTitle>
                            <StatsValue>{selectedFactory.name}</StatsValue>
                          </StatsContent>
                        </StatsCard>
                      </div>
                      <div className="col-md-4">
                        <StatsCard>
                          <StatsIcon color="#28c76f">
                            <MdOutlineDashboard />
                          </StatsIcon>
                          <StatsContent>
                            <StatsTitle>Total Areas</StatsTitle>
                            <StatsValue>{selectedFactory.areas?.length || 0}</StatsValue>
                          </StatsContent>
                        </StatsCard>
                      </div>
                      <div className="col-md-4">
                        <StatsCard>
                          <StatsIcon color="#ff9f43">
                            <FiLayers />
                          </StatsIcon>
                          <StatsContent>
                            <StatsTitle>Total Sub-Areas</StatsTitle>
                            <StatsValue>{subAreas?.length}</StatsValue>
                          </StatsContent>
                        </StatsCard>
                      </div>
                    </div>
                  )}

      <StyledCard>
        <CardHeader className="bg-white">
          <Row className="align-items-center">
            <Col md={6}>
              <h5 className="mb-0">All Areas</h5>
              <small className="text-muted">
                {loading ? 'Loading...' : `${areas.length} ${areas.length === 1 ? 'area' : 'areas'} in total`}
              </small>
            </Col>
            <Col md={6} className="text-md-right mt-2 mt-md-0 d-flex justify-content-end">
              <div className="position-relative d-inline-block">
                <FiFilter className="position-absolute" style={{ left: '12px', top: '10px', color: '#6e6b7b' }} size={16} />
                <FilterInput
                  type="text"
                  placeholder="Filter areas..."
                  value={filterText}
                  onChange={e => setFilterText(e.target.value)}
                  style={{ paddingLeft: '36px' }}
                />
              </div>
            </Col>
          </Row>
        </CardHeader>
        <CardBody>
          {loading ? (
            <div className="d-flex justify-content-center py-5">
              <Spinner color="primary" />
            </div>
          ) : filteredItemState.length === 0 ? (
            <EmptyStateContainer>
              <EmptyStateIcon>
                <FiMap size={48} />
              </EmptyStateIcon>
              <h4 className="mb-1" style={{ color: '#2c3e50' }}>No Areas Found</h4>
              <p className="text-muted mb-3">
                {filterText ? 'No matching areas found' : 'There are currently no areas'}
              </p>
              {!filterText && (
                <PrimaryButton onClick={() => setModalOpen(true)}>
                  <FiPlus size={18} />
                  Create New Area
                </PrimaryButton>
              )}
            </EmptyStateContainer>
          ) : (
            <DataTable
              columns={tableColumns}
              data={filteredItemState}
              pagination
              highlightOnHover
              responsive
              striped
              noHeader
              customStyles={{
                headCells: {
                  style: {
                    fontWeight: 600,
                    fontSize: '0.8125rem',
                    color: '#6e6b7b',
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px',
                    paddingLeft: '1.5rem',
                    paddingRight: '1.5rem',
                  },
                },
                cells: {
                  style: {
                    paddingLeft: '1.5rem',
                    paddingRight: '1.5rem',
                    paddingTop: '1rem',
                    paddingBottom: '1rem',
                  },
                },
                rows: {
                  style: {
                    '&:hover': {
                      backgroundColor: '#f8f7fa !important',
                    },
                  },
                },
              }}
              paginationPerPage={10}
              paginationRowsPerPageOptions={[10, 25, 50, 100]}
              paginationComponentOptions={{
                rowsPerPageText: 'Rows per page:',
                rangeSeparatorText: 'of',
                noRowsPerPage: false,
              }}
            />
          )}
        </CardBody>
      </StyledCard>

      <Add_area_modal
        modalOpen={modalOpen}
        setModalOpen={setModalOpen}
        form={form}
        setForm={setForm}
        handleInputChange={handleInputChange}
        handleAddArea={handleAddArea}
        formLoading={formLoading}
        factories={factories}
        type={modalType}
        handleupdateAreasubmission={handleupdateAreasubmission}
      />

      <ConfirmationModal
        modal={showconfirmationModal}
        toggle={taggle_confirmation_modal}
        body={`Are you sure you want to change status of ${confirmation_modal_data?.area_name} area?`}
        header={'Status Change Confirmation'}
        actionbtn={'Update'}
        handleConfirm={handleStatusToggle}
        type={'status change'}
      />
    </Container>
  );
}