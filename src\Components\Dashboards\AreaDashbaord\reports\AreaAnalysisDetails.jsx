import React, { Fragment, useState, useEffect, useRef, useContext } from "react";
import AlertList from "../../../Tables/DatatableForArea/DataTable/Index";
import liveAlertContext from '../../../../_helper/formData/LiveAlert/LiveAlert';
import {
  CardBody,
  CardHeader,
  Col,
  Container,
  Modal,
  Row,
} from "react-bootstrap";
import { H4 } from "../../../../AbstractElements";
import {
  areaOptionsData,
  areas,
  DateFilters,
  dummyDataForLiveAlerts,
  modulesforAlerts,
  modulesForRefrigeration,
  Shifts,
  statuses,
  subareaOptionsData,
  ViolationSeverity,
} from "../../../../Data/staticData/data";
import AreaService from "../../../../api/areaService";
import Loader1 from "../../../../CommonElements/Spinner/loader";
import Loader3 from "../../../../CommonElements/Spinner/loader3";
import moment from "moment";
import SingleImage from "../../../../Gallery/zoomin/SingleImage";
import CommonFIlterButton from "../../../Common/commonFilterButton/CommonFIlterButton";
import { Button, FormGroup, Input, Card } from "reactstrap";
import { errorToast, getWeek } from "../../../../_helper/helper";
import CameraImage from "../../../../assets/images/cameras/camera.jpeg";
import { Typeahead } from "react-bootstrap-typeahead";
import { dateChoose } from "../../../Screens/GlobalUser/AIModelReports/Components/data/staticData";
import LiveAlertsFilters from "./Components/DateFilters/LiveAlertsFilters";
import LiveAlertsCards from "./Components/LiveAlertsCards/LiveAlertsCards";
import "./Components/LiveAlertsCards/livealerts.css";
import './reports.css'
import axios from "axios";
import { IoCheckmarkOutline } from "react-icons/io5";
import { RxReset } from "react-icons/rx";
import { Filter } from "react-feather";
import TypeheadFilter from "../../../Common/commonFilterButton/TypeheadFilter";
import { ClipLoader } from "react-spinners";
import { getCurrentWeekWithYear } from "../../../../utils/currentWeekWithYear";
import { toast } from "react-toastify";
import { useLocation, useNavigate, useParams } from "react-router";
import { SlArrowLeft } from "react-icons/sl";
import NewHeatMap from '../../../Screens/GlobalUser/LiveAnalytics/New componens/Grapgh/NewHeatMap'
import FactoryCharts from "../Default/FactoryCharts";
import { formatMonth, formatWeek } from "../../../../utils/formatDate";
import { getSharedFilters, updateSharedFilters } from "../../../../utils/filters/shareFilters";


const formatDate = (dateStr) => {
  if (!dateStr) return "";
  const [year, month, day] = dateStr.split("-");
  return `${month}/${day}/${year}`;
};

const getLS = (key, fallback = null) => {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : fallback;
  } catch {
    return fallback;
  }
};

const setLS = (key, value) => localStorage.setItem(key, JSON.stringify(value));

const AreaAnalysisDetails = ({
  togglee,
  settogglee,
  acceptedArray,
  setAcceptedArray,
  rejectedArray,
  setRejectedArray,
  runApi,
  setRunApi,
  setQaFilters
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const filterCardRef = useRef(null);
  const filterButton = useRef(null);

  const analysis = getLS('areaPayload');
  const areaanalysisfilters = getLS('areaanalysisalertfilters', {});
  console.log('areaanalysisfilters', areaanalysisfilters)
  const userData = getLS('userData', {});
  const role = getLS('role');
  const factoryID = userData?.factory?.id || 0;
  const { id } = useParams();

  const { setLiveAlertData, settotalLiveAlerts, filtersContext, setFiltersContext, setmodelandreports } = useContext(liveAlertContext);

  const todayy = new Date().toISOString().split("T")[0];
  const currentWeekk = getCurrentWeekWithYear();
  const monthtoday = new Date();
  const shared = getSharedFilters()

  const [showFilters, setShowFilters] = useState(false);

  const [modules, setModules] = useState([]);
  const [initialMods, setInitialMods] = useState([]);
  const [severities, setSeverities] = useState(ViolationSeverity);
  const [pageCache, setPageCache] = useState({});
  const [showButtons, setShowButtons] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [imageData, setImageData] = useState({});
  const [loader, setLoader] = useState(true);

  const [selectedOption, setSelectedOption] = useState(
    areaanalysisfilters.week !== '' ? 'Week' :
      areaanalysisfilters.month !== '' ? 'Month' : ''
  );
  const [dateShow, setDateShow] = useState(false);
  const [monthShow, setMonthShow] = useState(areaanalysisfilters.month !== '');
  const [weeklyShow, setWeeklyShow] = useState(areaanalysisfilters.week !== '');
  const [customDate, setCustomDate] = useState(false);

  const [accept, setAccept] = useState([]);
  const [reject, setReject] = useState([]);
  const [summary, setsummary] = useState({});
  const [areaName, setAreaName] = useState("");
  const [filtereddData, setFiltereddData] = useState();
  const [pageNo, setPageNo] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [reset, setReset] = useState(false);
  const [areas, setAreas] = useState([]);
  const [subareas, setSubAreas] = useState({});

  const typeheadStyle = { width: '144px', fontSize: '13px', color: 'black' };
  const style = { minWidth: "132px", width: "144px", maxWidth: "150px", height: "38px", fontSize: 13, };

  const [newFilters, setNewFilters] = useState({
    approval: "Select Approval",
    module: "",
    severity: "",
    shift: areaanalysisfilters.shifts !== '' ? [areaanalysisfilters.shifts] : [],
    date: areaanalysisfilters.date,
    week: areaanalysisfilters.week,
    month: areaanalysisfilters.month,
    starting: areaanalysisfilters.starting,
    ending: areaanalysisfilters.ending,
    area: analysis?.name,
    subarea: "",
  });

  const [Factory_Charts_Filter, setFactory_Charts_Filter] = useState({
    month: areaanalysisfilters.month,
    week: areaanalysisfilters.week,
    shift: areaanalysisfilters.shifts,
    role: JSON.parse(localStorage.getItem('role')),
    starting: areaanalysisfilters?.starting ,
    ending: areaanalysisfilters?.ending,
    date:areaanalysisfilters.date
  })
    

  useEffect(() => {
    if (location?.state) {
      setAreaName(location?.state?.idData?.owner)
    }
  }, [location]);

  useEffect(() => {
    async function fetchModules() {
      const res = await AreaService.GetModules();
      setModules(res?.data?.data?.map((m) => m.module_name));
      setInitialMods(res?.data?.data?.map((m) => m.module_name));
    }
    async function fetchArea() {
      const res = await AreaService.GetAllAreas(factoryID);
      setAreas(res?.data?.data?.areas);
      const mappedAreas = res?.data?.data?.areas?.map(area => ({
        ...area,
        label: `${area.area}, ${area.areaOwner}`,
        disabled: !area.active
      })) || [];
      // Subareas
      const subs = mappedAreas.reduce((acc, a) => {
        acc[a.area] = a.sub_area;
        return acc;
      }, {});
      setSubAreas(subs);
    }
    fetchModules();
    fetchArea();
  }, []);

  useEffect(() => {
    let filters = getLS('filters');
    if (!filters) {
      console.log("-------------------------------xd------------------------")
    console.log(newFilters)
      setLS('filters', newFilters);
    } else {
      setNewFilters(filters);
      setShowButtons(
        filters.approval !== 'Select Approval' ||
        filters.module ||
        filters.severity ||
        (role !== 'area' && filters.area) ||
        filters.subarea ||
        filters.month ||
        filters.date ||
        filters.week !== currentWeekk ||
        filters.starting ||
        filters.ending
      );
      setMonthShow(!!filters.month);
      setDateShow(!!filters.date);
      setCustomDate(!!(filters.starting || filters.ending));
      setWeeklyShow(!!(!filters.month && !filters.date && !filters.starting && !filters.ending));
      setSelectedOption(
        filters.week ? 'Week' :
          filters.month ? 'Month' :
            filters.date ? 'Daily' :
              (filters.starting || filters.ending) ? 'Custom' : ''
      );
    }
  }, []);

  useEffect(() => {
    const filters = getLS('filters', newFilters);
    console.log('filtersNew', filters)
    if (!pageCache[pageNo]) {
      const payload = {
        user_id: 1,
        factory_id: factoryID,
        identifier:
          filters.date ? "date" :
            filters.week ? "week" :
              filters.month ? "month" :
                (filters.starting && filters.ending) ? "custom" : "no date",
        filters: filters,
        pagination: { page_no: pageNo, per_page: 21 }
      };
      const payload1 = {
        safety_area: [],
        factory_id: factoryID,
        shift: [filters.shift],
        start_date: filters.starting ? formatDate(filters.starting) : (filters.date ? formatDate(filters.date) : ""),
        end_date: filters.ending ? formatDate(filters.ending) : (filters.date ? formatDate(filters.date) : ""),
        week: filters.week,
        month: filters.month,
      };
      setLS('qafilters', payload);
      if (role === 'qa') {
        CallOps({
          ...payload1,
          safety_area: [filters.area],
          shift: Array.isArray(filters.shift) ? filters.shift : [filters.shift]
        });
      }
      fetchLive(payload, payload1);
    } else {
      setFiltereddData(pageCache[pageNo]);
    }
    setLS('pageNo', pageNo);
  }, [pageNo]);

  // Outside click for filter panel
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        filterCardRef.current &&
        filterButton.current &&
        !filterCardRef.current.contains(event.target) &&
        !filterButton.current.contains(event.target)
      ) {
        setShowFilters(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [showFilters]);


  const dateOptionMap = {
    Duration: { month: false, date: false, week: false, custom: false },
    Month: { month: true, date: false, week: false, custom: false },
    Daily: { month: false, date: true, week: false, custom: false },
    Week: { month: false, date: false, week: true, custom: false },
    Custom: { month: false, date: false, week: false, custom: true },
  };
  const handleDateDrop = (e) => {
    setSelectedOption(e.target.value);
    setShowButtons(true);
    const option = dateOptionMap[e.target.value] || {};
    setMonthShow(option.month || false);
    setDateShow(option.date || false);
    setWeeklyShow(option.week || false);
    setCustomDate(option.custom || false);
  };

  const severityModuleMap = {
    High: ["Vest", "Helmet", "Machine Guard"],
    Medium: ["Helmet", "Emergency Exit", "Vest", "MMHE"],
    "Medium plus": ["Emergency Exit", "Machine Guard", "MMHE"]
  };

  const moduleSeverityMap = {
    "MMHE": ViolationSeverity.filter(s => !["Medium", "Medium plus"].includes(s)),
    "Emergency Exit": ViolationSeverity.filter(s => !["Medium", "Medium plus"].includes(s)),
    "Vest": ViolationSeverity.filter(s => !["High", "Medium"].includes(s)),
    "Helmet": ViolationSeverity.filter(s => !["High", "Medium"].includes(s)),
    "Machine Guard": ViolationSeverity.filter(s => !["Medium plus", "High"].includes(s)),
  };

  const handleTypeChange = (selected) => {
    setShowButtons(true);
    setNewFilters((prev) => ({
      ...prev,
      shift: selected,
    }));
  };

  const handleNewInputChange = (e, field) => {
    let value = e.target.value;
    setShowButtons(true);

    let updatedFilters = { ...newFilters, [field]: value };
    if (field === "week") {
      updatedFilters = { ...updatedFilters, date: "", month: "", starting: "", ending: "" };
    } else if (field === "month") {
      updatedFilters = { ...updatedFilters, date: "", week: "", starting: "", ending: "" };
    } else if (field === "date") {
      updatedFilters = { ...updatedFilters, month: "", week: "", starting: "", ending: "" };
    } else if (field === "starting" || field === "ending") {
      updatedFilters = { ...updatedFilters, date: "", month: "", week: "" };
    }

    if (field === "severity") {
      const filteredModules = initialMods.filter(
        (mod) => !severityModuleMap[value]?.includes(mod)
      );
      setModules(filteredModules);
    }

    if (field === "module") {
      setSeverities(moduleSeverityMap[value] || ["High", "Medium plus", "Medium"]);
    }

    setNewFilters(updatedFilters);
    setLS("filters", updatedFilters);
  };


  const applyNewFilters = async () => {
    if (dateShow && !newFilters.date) return errorToast('Choose The Date');
    if (weeklyShow && !newFilters.week) return errorToast('Choose The Week');
    if (monthShow && !newFilters.month) return errorToast('Choose The Month');
    if (customDate && (!newFilters.starting || !newFilters.ending)) return errorToast('Choose Both ranges');

    setLoader(true);
    setPageNo(1);
    setPageCache({});

    const payload = {
      user_id: 1,
      factory_id: factoryID,
      identifier:
        newFilters.date ? "date" :
          newFilters.week ? "week" :
            newFilters.month ? "month" :
              (newFilters.starting && newFilters.ending) ? "custom" : "no date",
      filters: newFilters,
      pagination: { page_no: 1, per_page: 21 }
    };
    setLS('qafilters', payload);
    if (role === 'qa') setQaFilters(payload);

    const payload1 = {
      safety_area: [],
      factory_id: factoryID,
      shift: [newFilters.shift],
      start_date: newFilters.starting ? formatDate(newFilters.starting) : (newFilters.date ? formatDate(newFilters.date) : ""),
      end_date: newFilters.ending ? formatDate(newFilters.ending) : (newFilters.date ? formatDate(newFilters.date) : ""),
      week: newFilters.week,
      month: newFilters.month,
    };

    const payload2 = {
      ...payload1,
      safety_area: [newFilters.area],
      shift: Array.isArray(newFilters.shift) ? newFilters.shift : [newFilters.shift],
    };
    setFactory_Charts_Filter({
      week: newFilters.week,
      month: newFilters.month,
      shift: newFilters.shift?.length > 0 ? newFilters.shift?.[0] : '',
      role: JSON.parse(localStorage.getItem('role')),
      start_date: newFilters?.starting,
      end_date: newFilters?.ending,
      date: newFilters?.date
    })

    await fetchLive(payload, payload1);
    await CallOps(payload2);

    setLS("filters", newFilters);
    setLS('pageNo', 1);
    updateSharedFilters({
      weekly: newFilters?.week,
      month: newFilters?.month,
      shift: newFilters.shift?.length > 0 ? newFilters.shift?.[0] : '',
    })
  };

  function Reset() {
    setLoader(true);
    setShowButtons(false);
    setDateShow(false);
    setWeeklyShow(true);
    setMonthShow(false);
    setCustomDate(false);
    setSelectedOption("Week");
    setReset(true);
    setPageNo(1);
    setPageCache({});

    const resetFilters = {
      approval: "Select Approval",
      module: "",
      severity: "",
      shift: [],
      date: "",
      week: currentWeekk,
      month: "",
      starting: "",
      ending: "",
      area: analysis?.name,
      subarea: "",
    }
    setNewFilters(resetFilters);
    setFactory_Charts_Filter({
      shift: '',
      week: currentWeekk,
      month: '',
      role: JSON.parse(localStorage.getItem('role')),
      starting: '',
      ending: ''
    })
    setLS("filters", resetFilters);
    setLS("pageNo", 1);

    const payload = {
      user_id: 1,
      identifier: "week",
      factory_id: factoryID,
      filters: resetFilters,
      pagination: { page_no: 1, per_page: 21 },
    };
    if (role === 'qa') {
      setQaFilters(payload);
      setLS('qafilters', payload);
      const payload1 = {
        safety_area: [resetFilters.area],
        factory_id: factoryID,
        shift: Array.isArray(resetFilters.shift) ? resetFilters.shift : [resetFilters.shift],
        start_date: resetFilters.starting ? formatDate(resetFilters.starting) : (resetFilters.date ? formatDate(resetFilters.date) : ""),
        end_date: resetFilters.ending ? formatDate(resetFilters.ending) : (resetFilters.date ? formatDate(resetFilters.date) : ""),
        week: resetFilters.week,
        month: resetFilters.month,
      };
      CallOps(payload1);
    }
    const payload1 = {
      safety_area: [],
      factory_id: factoryID,
      shift: [resetFilters.shift],
      start_date: formatDate(todayy),
      end_date: formatDate(todayy),
      week: "",
      month: "",
    };
    fetchLive(payload, payload1);

    setModules(["Helmet", "Emergency Exit", "Machine Guard", "Vest", "MMHE"]);
    setSeverities(["High", "Medium plus", "Medium"]);
    setLS("severities", severities)
  }


  async function fetchLive(payload, payload1) {
    try {
      setLoader(true);
      const updated_payload = {
        ...payload,
        role: JSON.parse(localStorage.getItem('role'))
      }
      console.log('updatedPayload', updated_payload)
      setFiltersContext(updated_payload.filters);
      let res = await AreaService.getFilterAlerts(updated_payload);
      console.log('res22222', res)
      if (res) {
        setLoader(false);
        const fetchedData = res?.data?.data?.alerts;
        const { filters } = payload;
        setPageCache(prev => ({ ...prev, [payload.pagination.page_no]: fetchedData }));
        setLS('filters', filters);

        setFiltereddData(fetchedData);
        setLiveAlertData(fetchedData);

        // Summary
        let updatedState = {};
        if (filters.approval === 'Verified') {
          updatedState = {
            count: res.data.data.accepted_records + res.data.data.rejected_records,
            verified: res.data.data.accepted_records + res.data.data.rejected_records,
            pending: 0,
            acc: res.data.data.accepted_records,
            rej: res.data.data.rejected_records
          };
        } else if (filters.approval === 'Accepted') {
          updatedState = {
            count: res.data.data.accepted_records,
            verified: res.data.data.accepted_records,
            pending: 0,
            acc: res.data.data.accepted_records,
            rej: 0
          };
        } else if (filters.approval === 'Rejected') {
          updatedState = {
            count: res.data.data.rejected_records,
            verified: res.data.data.rejected_records,
            pending: 0,
            acc: 0,
            rej: res.data.data.rejected_records
          };
        } else if (filters.approval === 'Unverified') {
          const unverifiedRecords = res.data.data.total_records - (res.data.data.accepted_records + res.data.data.rejected_records);
          updatedState = {
            count: unverifiedRecords,
            verified: 0,
            pending: unverifiedRecords,
            acc: 0,
            rej: 0
          };
        } else {
          updatedState = {
            count: res.data.data.total_records,
            verified: res.data.data.accepted_records + res.data.data.rejected_records,
            pending: res.data.data.total_records - (res.data.data.accepted_records + res.data.data.rejected_records),
            acc: res.data.data.accepted_records,
            rej: res.data.data.rejected_records
          };
        }
        setsummary(updatedState);
        setmodelandreports((prev) => ({ ...prev, totalAlerts: updatedState.count }))
        setTotalPages(res.data.data.total_pages);
      }
    } catch (error) {
      console.log("Error fetching alerts:", error);
      setLoader(false);
    }
  }

  async function CallOps(payload1) {
    if (role === 'qa') {
      const resp = await AreaService.getOperationID(payload1);
      setAcceptedArray(resp?.data?.ids?.accepted);
      setRejectedArray(resp?.data?.ids?.rejected);
    }
  }


  function handleCardClick(item) {
    setImageData({
      photo: item.image,
      cameraName: item.camera_name,
      violation: item.violation,
      operation_safety_id: item.operation_safety_id,
      date: item?.date,
      time: item?.time,
    });
    setShowModal(!showModal);
  }

 const renderFilters = () => { 
  const transformFilterValue = (value, key) => { 
    if (key === 'month') {
      return formatMonth(value);
    }
    if (key === 'week') {
      return formatWeek(value);
    }
    return value;
  };

  const activeFilters = Object.entries(Factory_Charts_Filter)
    .filter(([key, value]) => ['shift', 'month', 'week',"date"].includes(key) && Boolean(value)) // changed 'weekly' -> 'week'
    .map(([key, value]) => transformFilterValue(value, key));

  return activeFilters.length >= 2
    ? `${activeFilters[0]} and ${activeFilters[1]}`
    : activeFilters.length === 1
      ? activeFilters[0]
      : "";
};

  return (
    <Fragment>
      <Container className="dashboard-first-page px-3" fluid={true}>
        {analysis && (
          <div style={{ paddingBlock: '16px' }} className='d-flex align-items-center gap-2'>
            <SlArrowLeft onClick={() => {
              localStorage.removeItem('filters');
              navigate(`${process.env.PUBLIC_URL}/areaanalysis/${role}`);
            }} style={{ fontSize: '15px', color: '#175FA4', cursor: 'pointer' }} />
            <p onClick={() => {
              localStorage.removeItem('filters');
              navigate(`${process.env.PUBLIC_URL}/areaanalysis/${role}`);
            }} style={{ fontSize: '20px', color: '#175FA4', cursor: 'pointer' }} className='m-0 p-0'>Back</p>
          </div>
        )}
        {loader ? (
          <div style={{ width: '100%', height: 'calc(100vh - 300px)' }} className="d-flex align-items-center justify-content-center w-100">
            <span> <Loader3 /></span>
          </div>
        ) : (
          <>
            <div className={`${role !== 'qa' && 'mb-2'}`}>
              <Row>
                <Col md='7' className="d-flex align-items-center">
                  <div className=''>
                    <H4>{location?.state?.idData?.owner}</H4>
                    <p className="m-0 f-light">{renderFilters() ? renderFilters() : 'Year 2025'}</p>
                  </div>
                </Col>
                <Col md='5' className="d-flex  justify-content-end">
                  <div type='button' className={`d-flex justify-content-center filter-btnn  ${showFilters && 'border_R'}`}
                    ref={filterButton}
                    onClick={() => setShowFilters(!showFilters)} >
                    <p className="m-0" style={{ fontSize: "16px" }}>Filters</p>
                    <span className="d-flex"><Filter color="#fff" size={16} className="ms-2 " /></span>
                  </div>
                </Col>
              </Row>
              <div className="w-100 d-flex justify-content-end position-relative">
                {showFilters && <div className={`d-flex align-items-center justify-content-end gap-2 py-3 filter-card shadow-sm`}
                  ref={filterCardRef}
                >
                  <div className="d-flex align-items-center justify-content-center flex-wrap gap-2">
                    <LiveAlertsFilters
                      analysis={analysis}
                      maxWeek={getCurrentWeekWithYear()}
                      role={role}
                      areas={areas}
                      subarea={subareas[newFilters?.area]}
                      modulesforAlerts={modules}
                      ViolationSeverity={severities}
                      handleDateDrop={handleDateDrop}
                      currentWeekk={currentWeekk}
                      style={style}
                      handleNewInputChange={handleNewInputChange}
                      newFilters={newFilters}
                      selectedOption={selectedOption}
                      dateShow={dateShow}
                      monthShow={monthShow}
                      weeklyShow={weeklyShow}
                      customDate={customDate}
                      typeHeadFilter={true}
                    />
                    <TypeheadFilter
                      typeheadStyle={typeheadStyle}
                      selected={newFilters?.shift}
                      shifts={Shifts}
                      placeholder={'Select Shift'}
                      handleTypeChange={handleTypeChange}
                    />
                    {showButtons && (
                      <div className="d-flex flex-wrap gap-2 justify-content-center">
                        <Button style={style} className={`m-0 p-0 rounded-3 shadow-sm d-flex align-items-center justify-content-evenly`} onClick={applyNewFilters} color="">
                          <IoCheckmarkOutline style={{ color: '#22c65e', fontSize: "20px", transform: "rotate(20deg)" }} />
                          <p style={{ color: '#22c65e' }} className="m-0 p-0 "> Accept</p>
                        </Button>
                        <Button style={style} className={`m-0 p-0 rounded-3 shadow-sm d-flex align-items-center justify-content-evenly`} onClick={Reset} color="">
                          <RxReset style={{ color: '#4e74d4', fontSize: "20px" }} />
                          <p style={{ color: '#4e74d4' }} className="m-0 p-0 "> Reset</p>
                        </Button>
                      </div>
                    )}
                  </div>
                </div>}
              </div>
            </div>
            {role === 'factory' && filtereddData?.length > 0 && (
              <FactoryCharts NoEvent={true} locationData={location?.state?.idData} filters={Factory_Charts_Filter} showFilters={false} />
            )}
            <div className={`d-flex align-items-center justify-content-between gap-2  mb-2`}>
              <p className="p-0 m-0 d-flex gap-1 flex-wrap align-items-center" style={{ fontSize: "20px", fontWeight: "600" }} >
                {areaName || 'Area Analysis'}
                <span style={{ fontSize: "18px", fontWeight: "100" }}>
                  | Alerts: {summary?.count ?? 'N/A'} - {
                    newFilters && typeof newFilters === 'object' ? (
                      newFilters.week && newFilters.week !== '' ? (
                        // Safely split week string
                        (() => {
                          const parts = newFilters.week.split('-W');
                          return parts.length > 1 ? `Week ${parts[1]}` : 'Week N/A';
                        })()
                      ) : newFilters.date && newFilters.date !== '' ? (
                        (() => {
                          const dateParts = newFilters.date.split('-');
                          return dateParts.length > 2 ? `Day ${dateParts[2]}` : 'Day N/A';
                        })()
                      ) : newFilters.month && newFilters.month !== '' ? (
                        (() => {
                          // Extract month and year safely
                          const match = newFilters.month.match(/^(\d{4})-(\d{2})$/);
                          if (!match) return 'Month N/A';
                          const year = match[1];
                          const monthNum = parseInt(match[2], 10);
                          const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
                          const monthName = monthNames[monthNum - 1] || 'Unknown';
                          const yearSuffix = year.slice(-2);
                          return `${monthName} ${yearSuffix}`;
                        })()
                      ) : (newFilters.starting && newFilters.ending) ? (
                        (() => {
                          const startParts = newFilters.starting.split('-');
                          const endParts = newFilters.ending.split('-');
                          const startDay = startParts.length > 2 ? startParts[2] : 'N/A';
                          const endDay = endParts.length > 2 ? endParts[2] : 'N/A';
                          return `Day ${startDay} to ${endDay}`;
                        })()
                      ) : 'Date N/A'
                    ) : 'Date N/A'
                  }
                </span>
              </p>

            </div>


            {role === 'qa' && (
              <div className="d-flex flex-wrap gap-3 justify-content-start mb-3 mt-1">
                <div className="d-flex flex-column align-items-center justify-content-center ">
                  <p style={{ color: '#71717a' }} className="p-0 ms-0 my-0 ">Verified: </p>
                  <p className='p-0 m-0 text-primary' style={{ fontWeight: '600', fontSize: "18px" }}>{summary.verified}</p>
                </div>
                <div className="border"></div>
                <div className="d-flex flex-column align-items-center justify-content-center ">
                  <p style={{ color: '#71717a' }} className="p-0 ms-0 my-0 ">Accepted: </p>
                  <p className='p-0 m-0 text-success' style={{ fontWeight: '600', fontSize: "18px" }}>{summary.acc}</p>
                </div>
                <div className="border"></div>
                <div className="d-flex flex-column align-items-center justify-content-center ">
                  <p style={{ color: '#71717a' }} className="p-0 ms-0 my-0 ">Rejected: </p>
                  <p className='p-0 m-0 text-danger' style={{ fontWeight: '600', fontSize: "18px" }}>{summary.rej}</p>
                </div>
                <div className="border"></div>
                <div className="d-flex flex-column align-items-center justify-content-center ">
                  <p style={{ color: '#71717a' }} className="p-0 ms-0 my-0 ">Pending: </p>
                  <p className='p-0 m-0 text-info' style={{ fontWeight: '600', fontSize: "18px" }}>{summary.pending}</p>
                </div>
              </div>
            )}

            <LiveAlertsCards
              setFiltereddData={setFiltereddData}
              loader={loader}
              togglee={togglee}
              settogglee={settogglee}
              summary={summary}
              setsummary={setsummary}
              setAcceptedArray={setAcceptedArray}
              setRejectedArray={setRejectedArray}
              accept={accept}
              setAccept={setAccept}
              reject={reject}
              setReject={setReject}
              runApi={runApi}
              setRunApi={setRunApi}
              acceptedArray={acceptedArray}
              rejectedArray={rejectedArray}
              setLoader={setLoader}
              role={role}
              key={pageNo}
              setPageNo={setPageNo}
              pageNo={pageNo}
              total_pages={totalPages}
              imageData={imageData}
              showModal={showModal}
              setShowModal={setShowModal}
              filtereddData={filtereddData}
              handleCardClick={handleCardClick}
            />
          </>
        )}
      </Container>
    </Fragment>
  );
};

export default AreaAnalysisDetails;
