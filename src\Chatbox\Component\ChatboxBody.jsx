import React, { useState, useEffect, useRef } from 'react';
import '../chatboxbody.css';
import { errorToast } from '../../_helper/helper';
import ChatboxApi from '../../api/chatbox';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import rehypeHighlight from 'rehype-highlight';
import 'highlight.js/styles/github.css';
import 'highlight.js/styles/atom-one-dark.css';

// Import the updated mic component
import MicConnectionWithWebSocket from '../MicWithWebsocket/MicConnectionWithWebsocket';

const markdownComponents = {
  p: ({ children }) => <p style={{ margin: '0 0 10px', lineHeight: '1.7', color: '#333' }}>{children}</p>,
  strong: ({ children }) => <strong style={{ fontWeight: '600', color: '#111' }}>{children}</strong>,
  em: ({ children }) => <em style={{ color: '#555' }}>{children}</em>,
  ul: ({ children }) => <ul style={{ paddingLeft: '20px', marginBottom: '10px', listStyleType: 'disc' }}>{children}</ul>,
  ol: ({ children }) => <ol style={{ paddingLeft: '20px', marginBottom: '10px' }}>{children}</ol>,
  li: ({ children }) => <li style={{ marginBottom: '4px' }}>{children}</li>,
  a: ({ href, children }) => (
    <a href={href} target="_blank" rel="noreferrer" style={{ color: '#1769aa', textDecoration: 'underline' }}>
      {children}
    </a>
  ),
  h1: ({ children }) => <h1 style={{ fontSize: '20px', fontWeight: 700, margin: '20px 0 10px', color: '#222' }}>{children}</h1>,
  h2: ({ children }) => <h2 style={{ fontSize: '18px', fontWeight: 600, margin: '18px 0 8px', color: '#333' }}>{children}</h2>,
  h3: ({ children }) => <h3 style={{ fontSize: '16px', fontWeight: 600, margin: '16px 0 6px', color: '#444' }}>{children}</h3>,
  code: ({ children }) => (
    <code style={{ backgroundColor: '#f2f2f2', padding: '2px 6px', borderRadius: '4px', fontFamily: 'monospace', fontSize: '13px', color: '#c7254e' }}>
      {children}
    </code>
  ),
  pre: ({ children }) => (
    <pre style={{ backgroundColor: '#f6f8fa', padding: '12px', borderRadius: '6px', overflowX: 'auto', marginBottom: '10px', fontSize: '13px' }}>
      {children}
    </pre>
  ),
  blockquote: ({ children }) => (
    <blockquote style={{ borderLeft: '4px solid #ccc', paddingLeft: '10px', margin: '10px 0', color: '#555', fontStyle: 'italic', backgroundColor: '#f9f9f9' }}>
      {children}
    </blockquote>
  ),
  table: ({ children }) => (
    <DragScroll className="chat-table-wrap">
      <table className="chat-table">{children}</table>
    </DragScroll>
  ),
  thead: ({ children }) => <thead>{children}</thead>,
  tbody: ({ children }) => <tbody>{children}</tbody>,
  tr: ({ children }) => <tr>{children}</tr>,
  th: ({ children }) => <th style={{ background: '#f2f2f2', fontWeight: 'bold' }}>{children}</th>,
  td: ({ children }) => <td>{children}</td>,
};

function DragScroll({ children, className = '', style = {} }) {
  const wrapRef = useRef(null);
  const [isDown, setIsDown] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);

  const onMouseDown = (e) => {
    const el = wrapRef.current;
    if (!el) return;
    setIsDown(true);
    setStartX(e.pageX - el.offsetLeft);
    setScrollLeft(el.scrollLeft);
    document.body.style.userSelect = 'none';
    document.body.style.cursor = 'grabbing';
  };

  const onMouseLeave = () => {
    if (!isDown) return;
    setIsDown(false);
    document.body.style.userSelect = '';
    document.body.style.cursor = '';
  };

  const onMouseUp = () => {
    if (!isDown) return;
    setIsDown(false);
    document.body.style.userSelect = '';
    document.body.style.cursor = '';
  };

  const onMouseMove = (e) => {
    const el = wrapRef.current;
    if (!el || !isDown) return;
    e.preventDefault();
    const x = e.pageX - el.offsetLeft;
    const walk = (x - startX);
    el.scrollLeft = scrollLeft - walk;
  };

  const onWheel = (e) => {
    const el = wrapRef.current;
    if (!el) return;
    if (Math.abs(e.deltaY) > Math.abs(e.deltaX)) {
      el.scrollLeft += e.deltaY;
      e.preventDefault();
    }
  };

  return (
    <div
      ref={wrapRef}
      className={`drag-scroll ${className}`}
      style={style}
      onMouseDown={onMouseDown}
      onMouseLeave={onMouseLeave}
      onMouseUp={onMouseUp}
      onMouseMove={onMouseMove}
      onWheel={onWheel}
    >
      {children}
    </div>
  );
}

export default function ChatboxBody({ 
  prompt, 
  setPrompt, 
  messages, 
  setMessages, 
  loading, 
  setLoading, 
  setShowFullInput, 
  showFullInput 
}) {
  const [inputValue, setInputValue] = useState('');
  const messagesEndRef = useRef(null);
  const textareaRef = useRef(null);
  const footerRef = useRef(null);
  const [chatState, setChatState] = useState([]);
  const [memoryState, setMemoryState] = useState([]);
  const [sessionId, setSessionId] = useState(null);

  const getStartStream = async (queryText = null) => {
    try {
      const payload = {
        user_query: queryText || inputValue,
        chat_state: chatState,
        memory_state: memoryState,
      };
      const response = await ChatboxApi.getStreamID(payload);
      setSessionId(response.data.session_id);
      return response.data.session_id;
    } catch (error) {
      console.log('error', error);
      return errorToast('Error while starting the chat');
    }
  };

  // Used by MicStreamer to get a session without relying on inputValue
  const getOrCreateSessionId = async () => {
    if (sessionId) return sessionId;
    const payload = {
      user_query: '', // empty voice-init
      chat_state: chatState,
      memory_state: memoryState,
    };
    try {
      const response = await ChatboxApi.getStreamID(payload);
      const newSessionId = response.data.session_id;
      setSessionId(newSessionId);
      return newSessionId;
    } catch (e) {
      errorToast('Error while starting voice session');
      throw e;
    }
  };

  const handleSubmit = async (e, customText = null) => {
    e.preventDefault?.();
    const queryText = customText || inputValue.trim();
    if (!queryText) return;

    setLoading(true);

    const newUserMessage = {
      text: queryText,
      sender: 'user',
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
    };
    setMessages((prev) => [...prev, newUserMessage]);

    const placeholderMsg = {
      text: 'Analyzing...',
      sender: 'ai',
      timestamp: '',
      isTyping: true,
    };
    setMessages((prev) => [...prev, placeholderMsg]);

    const sid = await getStartStream(queryText);
    if (!sid) {
      setLoading(false);
      return;
    }

    try {
      const eventSource = new EventSource(`https://beunileverchatbot.disruptlabs.tech/chat/stream?session_id=${sid}`);
      
      eventSource.onmessage = (event) => {
        if (event.data && event.data.trim().startsWith('{')) {
          const parsed = JSON.parse(event.data);
          if (parsed.partial_response !== undefined) {
            setMessages((prev) => {
              const updated = [...prev];
              const last = updated.length - 1;
              if (updated[last]?.sender === 'ai') {
                updated[last] = {
                  ...updated[last],
                  text: parsed.partial_response,
                  timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                  isTyping: true,
                };
              }
              return updated;
            });
          }
          if (parsed.final) {
            if (parsed.chat_state) setChatState(parsed.chat_state);
            if (parsed.memory_state) setMemoryState(parsed.memory_state);
            
            // Mark final message
            setMessages((prev) => {
              const updated = [...prev];
              const last = updated.length - 1;
              if (updated[last]?.sender === 'ai') {
                updated[last].isTyping = false;
              }
              return updated;
            });
            
            eventSource.close();
            setLoading(false);
          }
        }
      };

      eventSource.onerror = (error) => {
        console.error('SSE error:', error);
        eventSource.close();
        setLoading(false);
        
        setMessages((prev) => {
          const updated = [...prev];
          const last = updated.length - 1;
          if (updated[last]?.sender === 'ai') {
            updated[last] = {
              ...updated[last],
              text: '[Error] Connection lost. Please try again.',
              isTyping: false,
            };
          }
          return updated;
        });
      };

      eventSource.addEventListener('end', () => {
        setMessages((prev) => {
          const updated = [...prev];
          const last = updated.length - 1;
          if (updated[last]?.sender === 'ai') {
            updated[last].text = updated[last].text.replace(/\.\.\.$/, '');
            updated[last].isTyping = false;
          }
          return updated;
        });
        eventSource.close();
        setLoading(false);
      });

    } catch (error) {
      console.error('Error in handleSubmit:', error);
      setLoading(false);
      errorToast('Failed to send message');
    }

    // Clear input only if not from voice
    if (!customText) {
      setInputValue('');
    }
  };

  // When MicStreamer emits a final transcript, submit it via the same flow
  const handleFinalTranscript = (text) => {
    setInputValue(text); // Update input value when final transcript is received
    setTimeout(() => {
      handleSubmit({ preventDefault: () => {} }, text);
    }, 100);
  };

  const handlePartialTranscript = (text) => {
    // Show partial transcripts in real-time
    setInputValue(text); // Update input value with partial transcript
  };

  const handleClear = async () => {
    try {
      // Call backend clear endpoint
      await fetch(`https://beunileverchatbot.disruptlabs.tech/chat/clear`, { 
        method: "POST" 
      });
      
      // Reset local state
      setInputValue('');
      setMessages([]);
      setSessionId(null);
      setChatState([]);
      setMemoryState([]);
      
      console.log('Chat cleared successfully');
    } catch (error) {
      console.error('Error clearing chat:', error);
      errorToast('Failed to clear chat');
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  useEffect(() => {
    if (showFullInput && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [showFullInput]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (footerRef.current && !footerRef.current.contains(event.target) && showFullInput) {
        setShowFullInput(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showFullInput, setShowFullInput]);

  return (
    <div className={`chatbox-container ${messages.length === 0 ? '' : ''}`}>
      <div className="chat-messages-section">
        {messages.map((msg, index) => (
          <div key={index} className={`message-bubble ${msg.sender === 'user' ? 'user' : 'ai'}`}>
            {msg.sender === 'ai' ? (
              <div>
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  rehypePlugins={[rehypeRaw, rehypeHighlight]}
                  components={markdownComponents}
                >
                  {msg.text}
                </ReactMarkdown>
                {msg.isTyping && (
                  <div style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    gap: '4px', 
                    marginTop: '8px',
                    opacity: 0.7 
                  }}>
                    <div className="typing-dot"></div>
                    <div className="typing-dot"></div>
                    <div className="typing-dot"></div>
                    <span style={{ fontSize: '12px' }}>Typing...</span>
                  </div>
                )}
              </div>
            ) : (
              <p style={{ margin: 0, color: '#fff' }}>{msg.text}</p>
            )}
            <div className="timestamp">{msg.timestamp}</div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {!showFullInput && (
        <div className="initial-prompt-box" onClick={() => setShowFullInput(true)}>
          <input 
            type="text" 
            className="initial-input" 
            placeholder="Message to AI agent..." 
            value={inputValue}  // Bind input value here
            readOnly 
          />
        </div>
      )}

      <div>
        <div className={`transition-footer ${showFullInput ? 'expanded' : 'collapsed'}`} ref={footerRef}>
          <form onSubmit={handleSubmit} className="chat-input-form">
            <textarea
              ref={textareaRef}
              className="chat-input"
              name="user-prompt"
              id="user-prompt"
              cols="30"
              rows="2"
              placeholder="Message to AI agent..."
              value={inputValue}  // Bind input value here
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              disabled={loading}
            />
            <div className="chat-input-buttons mt-3" style={{ gap: 8, display: 'flex', alignItems: 'center' }}>
              <button type="button" className="btn border" onClick={handleClear} disabled={loading}>
                Clear
              </button>

              <div style={{ position: 'relative' }}>
                <MicConnectionWithWebSocket
                  apiUrl="https://beunileverchatbot.disruptlabs.tech"
                  sessionId={sessionId}
                  getOrCreateSessionId={getOrCreateSessionId}
                  onPartialTranscript={handlePartialTranscript}
                  onFinalTranscript={handleFinalTranscript}
                  disabled={loading}
                  autoSubmitFinal={true}
                  showTranscriptUI={false}
                />
              </div>

              <button 
                type="submit" 
                disabled={loading || !inputValue.trim()} 
                className="btn" 
                style={{ background: '#3470a1', color: 'white', opacity: loading ? 0.6 : 1 }}
              >
                {loading ? 'Sending...' : 'Send ➤'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
