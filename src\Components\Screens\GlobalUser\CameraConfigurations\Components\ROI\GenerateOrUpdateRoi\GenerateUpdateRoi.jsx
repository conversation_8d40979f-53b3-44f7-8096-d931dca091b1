import React, { useEffect, useState } from 'react'
import GetCordinatesOfXY from '../ExtractCoordinates/Index'
import { isArrayValid, normalizeRoisWithModules, transformData } from '../../../../../../../utils/formatDate'
import { Card, CardBody } from 'reactstrap'
import DataTableOfSelectedROI from '../../DataTableOfSelectedROI'
import { MdCancel } from 'react-icons/md'
import { P } from '../../../../../../../AbstractElements'
import { BeatLoader } from 'react-spinners'
import { errorToast } from '../../../../../../../_helper/helper'
import { BiSolidError } from 'react-icons/bi'

export default function GenerateUpdateRoi({ img, details, modules, setROIObject, handleSubmit, smallloader, allmodules }) {
    const [FinalRoiSelection, setFinalRoiSelection] = useState([])
    const [Cordinates, setCordinates] = useState([])
    const [ExtractTableData, setExtractTableData] = useState([])
    const [ShowTable, setShowTable] = useState(false);
    const [NormaliseRoisData, setNormaliseRoisData] = useState({
        normalizedRois: [],
        moduleWiseSelections: []
    })
    const [tableType, settableType] = useState("add")
    // console.log("------------------details--------------------")
    // console.log(details)
    // console.log("------------------modules--------------------")
    // console.log(modules)
    // console.log("------------------allmodules-----------------")
    // console.log(allmodules)

    const HandleROIselection = (data) => {
        setFinalRoiSelection(prevSelection => {
            const roiExists = prevSelection.some(item => item.id === data.id);
            if (roiExists) {
                return prevSelection.filter(item => item.id !== data.id);
            } else {
                return [...prevSelection, data];
            }
        });
    }
    const CheckRoiIDinFinalSelection = (id) => {
        const roi = FinalRoiSelection.filter(item => item.id == id);
        if (roi.length > 0) {
            return true
        }
        else return false
    }
    const DeleteSelectedROI = (data) => {
        const For_Coordinates = Cordinates?.filter(item => item.id != data.id);
        const For_final_Coordinates = FinalRoiSelection?.filter(item => item.id != data.id);
        setCordinates(For_Coordinates);
        setFinalRoiSelection(For_final_Coordinates)
        if(For_final_Coordinates.length == 0){
            // beacuse roi is empty so we need to select roi first for the table and to generate table data
            setShowTable(false)
            settableType("add")
        }
    }

    function handleTableDataSubmission() {
        const check = isArrayValid(ExtractTableData);
        if (check) {
            const data = transformData(ExtractTableData, allmodules)
            if (data) {
                handleSubmit(data)
            }
            setROIObject(data)
            // successToast('Data successfully submited')
        }
        else {
            errorToast("Please fill in all required fields before proceeding");

        }
    }

    useEffect(() => {
        if (details?.roi_status == true) {
            console.log(details.roi_data)
            const { normalizedRois, moduleWiseSelections } = normalizeRoisWithModules(details.roi_data)
            console.log(normalizedRois, moduleWiseSelections, "updated")
            setCordinates(normalizedRois)
            setFinalRoiSelection(normalizedRois)
            setShowTable(true)
            setNormaliseRoisData({
                normalizedRois,
                moduleWiseSelections
            })
            settableType("edit")
        }
        else {
            // console.log("--------------------------")
            // console.log("Roi status false")
            settableType("add")
        }
    }, [details])

    return (
        <div className='pb-4'>
            <h5>Roi Selection</h5>
            {img ? <>
                <P>{details.roi_status == true ? "Roi found." : "Image received. You can now set the ROI."}</P>
                <GetCordinatesOfXY
                    imgSrc={img}
                    setCordinates={setCordinates}
                    Cordinates={Cordinates}
                />
                <div className='w-100 d-flex justify-content-between align-items-center mt-3'>
                    <div>
                        <div className='gap-2 d-flex'>
                            {Cordinates.map((item, key) => (
                                <div className='position-relative' key={key}>
                                    <MdCancel color='#1a3d6f' style={{ position: 'absolute', right: '0', zIndex: '2', cursor: 'pointer' }} onClick={() => DeleteSelectedROI(item)} />
                                    <button style={{ marginTop: "5px" }} className={` ${CheckRoiIDinFinalSelection(item.id) ? "ROISelected" : "ROIBTNS"}`} onClick={() => HandleROIselection(item)}>
                                        {item.name}
                                    </button>
                                </div>
                            ))}
                        </div>
                        {Cordinates?.length > 0 && FinalRoiSelection?.length <= 0 && <p className='clearparainfo mt-2'>Please select final ROIs</p>}

                    </div>
                    {Cordinates?.length > 0 && <button className='ClearSelectionBTN' onClick={() => { setFinalRoiSelection([]); setShowTable(false); setCordinates([]); settableType("add") }}>Clear selection</button>}
                </div>
                {FinalRoiSelection?.length > 0 && <div className='d-flex w-100 justify-content-end'>
                    <button className='NextBTN my-4' onClick={() => { setShowTable(true) }}>Next </button>
                </div>}
                {ShowTable && FinalRoiSelection?.length > 0 &&
                    <DataTableOfSelectedROI
                        modules={allmodules?.models}
                        allCordinates={FinalRoiSelection}
                        selectedmodules={modules}
                        setExtractTableData={setExtractTableData}
                        type={tableType}
                        NormaliseRoisData={NormaliseRoisData}
                        userCurrentSelectedRoi={Cordinates}
                    />
                }
                {ShowTable && <>
                    {smallloader ? <BeatLoader className="pull-right mt-3 mb-4" /> :
                    <div className='d-flex justify-content-end w-100 mb-2 mt-3'>
                        <button className='btn btn-primary' onClick={handleTableDataSubmission}>Save changes</button>
                    </div>
                } 
                </>}
                </> :
                <Card>
                    <CardBody>
                        <p className='text-center my-4 d-flex justify-content-center align-items-center ' > <BiSolidError color='#1e67d6' className='me-2' size={20} height={20} /> Image request is pending. Please wait, it will be processed soon.</p>

                    </CardBody>
                </Card>
            }
        </div>
    )
}
