import { useState, useEffect } from "react";
import {Card,CardBody,CardHeader,Form,FormGroup,Label,Input,<PERSON><PERSON>,Al<PERSON>,<PERSON>,<PERSON><PERSON><PERSON><PERSON>} from "reactstrap";
import { ResetHelper } from "./ResetHelper";
import './Forgotpassword.css'
import { Link } from "react-router-dom";
const Forgotpassword = () => {
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState("");
  const [isFocused, setIsFocused] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // Animation effect for the card
  useEffect(() => {
    const card = document.querySelector(".reset-card");
    setTimeout(() => {
      card?.classList.add("show");
    }, 100);
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");

    // Email validation
    if (!email || !/^\S+@\S+\.\S+$/.test(email)) {
      setError("Please enter a valid email address");
      const input = document.querySelector(".email-input");
      input?.classList.add("shake");
      setTimeout(() => {
        input?.classList.remove("shake");
      }, 500);
      return;
    }

    setIsSubmitting(true);

    try {
      await ResetHelper(email);
      setIsSuccess(true);
      const successIcon = document.querySelector(".success-icon");
      successIcon?.classList.add("pulse");
    } catch (err) {
      console.error("Reset password failed:", err.message);
      setError("Email not found in Records");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="reset-password-container">
      <div className="floating-shapes">
        <div className="shape shape-1"></div>
        <div className="shape shape-2"></div>
        <div className="shape shape-3"></div>
        <div className="shape shape-4"></div>
      </div>

      <Card className="reset-card">
        <CardHeader className="text-center border-0 bg-transparent">
          <div className="logo-container">
            <div className="logo-circle">
              <i className="lock-icon"></i>
            </div>
          </div>
          <h3 className="card-title">Reset Your Password</h3>
          <p className="card-subtitle">
            Enter your email address and we'll send you a link to reset your
            password
          </p>
        </CardHeader>

        <CardBody>
          {isSuccess ? (
            <div className="success-container text-center">
              <div className="success-icon">
                <i className="check-icon"></i>
              </div>
              <h3 className="mt-4">Email Sent!</h3>
              <p className="success-message">
                Password reset link has been sent to your email address. Please
                check your inbox and follow the instructions.
              </p>
            </div>
          ) : (
            <Form onSubmit={handleSubmit}>
              <FormGroup>
                <Label for="email" className="form-label">
                  Email Address
                </Label>
                <div
                  className={`input-container ${isFocused ? "focused" : ""} ${isHovered ? "hovered" : ""
                    }`}
                >
                  <i className="email-icon"></i>
                  <Input
                    type="email"
                    name="email"
                    id="email"
                    className="email-input custom-input"
                    placeholder="Enter your email address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    onFocus={() => setIsFocused(true)}
                    onBlur={() => setIsFocused(false)}
                    onMouseEnter={() => setIsHovered(true)}
                    onMouseLeave={() => setIsHovered(false)}
                    required
                  />
                 
                </div>

                {error && (
                  <Alert color="danger" className="error-message mt-3">
                    <i className="error-icon"></i> {error}
                  </Alert>
                )}
              </FormGroup>

              <Button
                color="primary"
                block
                className="submit-button"
                disabled={isSubmitting}
                type="submit"
              >
                {isSubmitting ? (
                  <>
                    <Spinner size="sm" className="mr-2" /> Sending...
                  </>
                ) : (
                  "Reset Password"
                )}
              </Button>
            </Form>
          )}
        </CardBody>

        <CardFooter className="text-center border-0 bg-transparent">
          {isSuccess ? (
            <Button
              color="link"
              className="back-button"
              onClick={() => {
                setIsSuccess(false);
                setEmail("");
              }}
            >
              Back
            </Button>
          ) : (
            <Link to={`${process.env.PUBLIC_URL}/login`} >Remember your password?Login</Link>
          )}
        </CardFooter>
      </Card>

      
    </div>
  );
};

export default Forgotpassword;