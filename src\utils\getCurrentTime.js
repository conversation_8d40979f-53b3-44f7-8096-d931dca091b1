export const getCurrentTime = () => {
    const now = new Date();
    const options = {
        hour: 'numeric',
        minute: '2-digit',
        second: '2-digit',
        hour12: true,
        timeZone: 'Asia/Karachi' // Pakistan Standard Time
    };

    const formattedTime = now.toLocaleTimeString('en-US', options);
    return formattedTime;
}

export function getCurrentDate() {
  const now = new Date();
  const day   = String(now.getDate()).padStart(2, '0');
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const year  = now.getFullYear();
  return `${day}-${month}-${year}`;
}