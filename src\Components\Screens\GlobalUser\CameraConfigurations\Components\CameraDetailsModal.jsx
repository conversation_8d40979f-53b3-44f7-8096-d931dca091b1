import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>dalBody, Row, Col, Badge } from 'reactstrap';
import { Clock, Camera, Monitor, Server, MapPin, User, Settings, Wifi, Calendar } from 'react-feather';
import CameraService from '../../../../../api/cameraService';
import DataTable from 'react-data-table-component'; 
import FactoryDataTable from '../../../../Dashboards/CompanyDashbaord/Components/Table/DataTable';
import { errorToast } from '../../../../../_helper/helper';

const CameraDetailsModal = ({ isOpen, toggle, cameraData }) => {
  const [cameraLogs, setcameraLogs] = useState([]) 
  const [loadinglogs, setloadinglogs] = useState(false)
  const getCameraLogs=async()=>{
    setloadinglogs(true)
    try {
    const res = await CameraService.getcameraLogs(cameraData?.camera_id); 
    const updatedLogs=res.data.data?.reverse()
    setcameraLogs(updatedLogs)
    setloadinglogs(false)
    } catch (error) {
      console.log(error)
      errorToast('Error while fetching logs')
      setloadinglogs(false)
    }

  }
  useEffect(() => {
    if (!cameraData) return 
    else if(isOpen==true){
      getCameraLogs()
    }
    else{
      setcameraLogs([])
    }
  }, [isOpen])
  

const activityColumns = [
    {
    name: 'Date',
    selector: row => row.start_time?.split(' ')[0],
    sortable: true,
  },,
  {
    name: 'Start Time',
    selector: row => row.start_time?.split(' ')[1],
    sortable: true,
  },
  {
    name: 'End Time',
    selector: row => row.end_time?.split(' ')[1],
    sortable: true,
  },
  {
    name: 'Status',
    selector: row => row.status,
    sortable: true,
    cell: row => (
      <span className={`badge rounded-pill bg-${row.status === 'active' ? 'success' : 'danger'}`}>
        {row.status.toUpperCase()}
      </span>
    )
  },
];

  return (
    <Modal isOpen={isOpen} toggle={toggle} size="lg" className="camera-details-modal">
      <ModalHeader toggle={toggle} className="border-bottom">
        <div className="d-flex align-items-center">
          <Camera size={20} className="text-primary me-2" />
          <h5 className="m-0 fw-bold">{cameraData?.camera_name || 'Camera Details'}</h5>
        </div>
      </ModalHeader>
      <ModalBody className="pt-4 pb-4">
        <div className="camera-details-container">
          {/* Main Info Section */}
          <div className="info-section main-info mb-4">
            <div className="section-header">
              <h6 className="section-title">
                <Monitor size={18} className="me-2" />
                Camera Information
              </h6>
            </div>
            <div className="section-content">
              <Row>
                <Col md="6" className="mb-3">
                  <div className="detail-item">
                    <span className="detail-label">Camera ID</span>
                    <span className="detail-value highlight">{cameraData?.camera_id || 'N/A'}</span>
                  </div>
                </Col>
                <Col md="6" className="mb-3">
                  <div className="detail-item">
                    <span className="detail-label">Status</span>
                    <Badge color={cameraData?.active ? "success" : "danger"} pill className="status-badge " style={{width:"100px"}}>
                      {cameraData?.active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </Col>
                <Col md="6" className="mb-3">
                  <div className="detail-item">
                    <span className="detail-label">Camera IP</span>
                    <span className="detail-value code">{cameraData?.camera_ip || 'N/A'}</span>
                  </div>
                </Col>
                <Col md="6" className="mb-3">
                  <div className="detail-item">
                    <span className="detail-label">Camera Brand</span>
                    <span className="detail-value">{cameraData?.camera_brand || 'N/A'}</span>
                  </div>
                </Col>
              </Row>
            </div>
          </div>

          {/* Location Section */}
          <div className="info-section location-info mb-4">
            <div className="section-header">
              <h6 className="section-title">
                <MapPin size={18} className="me-2" />
                Location Information
              </h6>
            </div>
            <div className="section-content">
              <Row>
                <Col md="4" className="mb-3">
                  <div className="detail-item">
                    <span className="detail-label">Area</span>
                    <span className="detail-value">{cameraData?.area || 'N/A'}</span>
                  </div>
                </Col>
                <Col md="4" className="mb-3">
                  <div className="detail-item">
                    <span className="detail-label">Sub Area</span>
                    <span className="detail-value">{cameraData?.sub_area || 'N/A'}</span>
                  </div>
                </Col>
                <Col md="4" className="mb-3">
                  <div className="detail-item">
                    <span className="detail-label">Area Owner</span>
                    <div className="d-flex align-items-center">
                      <User size={14} className="text-secondary me-1" />
                      <span className="detail-value">{cameraData?.area_owner || 'N/A'}</span>
                    </div>
                  </div>
                </Col>
              </Row>
            </div>
          </div>

          {/* Connection Details */}
          <div className="info-section connection-info mb-4">
            <div className="section-header">
              <h6 className="section-title">
                <Wifi size={18} className="me-2" />
                Connection Details
              </h6>
            </div>
            <div className="section-content">
              <Row>
                <Col md="3" className="mb-3">
                  <div className="detail-item">
                    <span className="detail-label">Username</span>
                    <span className="detail-value code">{cameraData?.username || 'N/A'}</span>
                  </div>
                </Col>
                <Col md="3" className="mb-3">
                  <div className="detail-item">
                    <span className="detail-label">Port</span>
                    <span className="detail-value">{cameraData?.port || 'N/A'}</span>
                  </div>
                </Col>
                <Col md="3" className="mb-3">
                  <div className="detail-item">
                    <span className="detail-label">Stream</span>
                    <span className="detail-value">{cameraData?.stream || 'N/A'}</span>
                  </div>
                </Col>
                <Col md="3" className="mb-3">
                  <div className="detail-item">
                    <span className="detail-label">NVR No.</span>
                    <span className="detail-value">{cameraData?.nvr_no || 'N/A'}</span>
                  </div>
                </Col>
              </Row>
            </div>
          </div>

          {/* Module Timing - Redesigned as cards */}
          <div className="info-section module-timing-info">
            <div className="section-header">
              <h6 className="section-title">
                <Settings size={18} className="me-2" />
                Module Configuration
              </h6>
            </div>
            <div className="section-content">
              {cameraData?.module_timing && cameraData?.module_timing.length > 0 ? (
                <div className="module-cards">
                  <Row>
                    {cameraData?.module_timing.map((module, index) => (
                      <Col lg="4" key={index} className="mb-3">
                        <div className="module-card">
                          <div className="module-card-header">
                            {/* <span className="module-id">#{module.module_id}</span> */}
                            <h6 className="module-name">{module.module_name}</h6>
                          </div>
                          <div className="module-card-body">
                            <div className="time-schedule">
                              <div className="schedule-icon">
                                <Clock size={18} className="text-primary" />
                              </div>
                              <div className="schedule-details">
                                <span className="schedule-label">Active Hours</span>
                                <div className="time-range">
                                  <span className="start-time">{module.start_time}</span>
                                  <span className="time-separator">to</span>
                                  <span className="end-time">{module.end_time}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </Col>
                    ))}
                  </Row>
                </div>
              ) : (
                <div className="empty-state">
                  <Server size={24} className="text-muted mb-2" />
                  <p className="text-muted">No module timing information available</p>
                </div>
              )}
            </div>
          </div>
          

 
            <div className="info-section activity-log-table mt-4">
              <div className="section-header mb-2">
                <h6 className="section-title d-flex align-items-center">
                  <Calendar size={18} className="me-2" />
                  Camera Activity Logs
                </h6>
              </div>
              {loadinglogs?<div className='w-100 d-flex justify-content-center my-3'>
                <div className='spinner'></div>
              </div>: <FactoryDataTable staticData={cameraLogs} tableColumns={activityColumns} /> }
              {/* <DataTable
                columns={activityColumns}
                data={cameraLogs}
                dense
                striped
                highlightOnHover
                responsive
                pagination
              /> */}
            </div> 


        </div>
      </ModalBody>
    </Modal>
  );
};

export default CameraDetailsModal;

