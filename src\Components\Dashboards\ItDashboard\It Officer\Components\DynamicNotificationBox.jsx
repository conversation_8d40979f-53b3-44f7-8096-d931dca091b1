import React, { useEffect } from 'react'
import { FormGroup, Input, Label } from 'reactstrap'
import { Typeahead } from "react-bootstrap-typeahead";
import 'react-bootstrap-typeahead/css/Typeahead.css';
import { GenerateSub_AreaOptions, handleTypeaheadChange } from '../../HelpingFunctions/HelpingFunctions';

export default function DynamicNotificationBox({ flag, label, type, name, checked, onChange, AreaOptions, Data, FormData, setFormData, AllFactoryIds, All_Factories, errors }) {
  
    return (
        <div className='border mb-2 p-3 b-r-10'>
            <FormGroup switch className=" mb-3">
                <Input type={type} name={name} id={name} checked={checked} onChange={onChange} />
                <Label >{label}</Label>
            </FormGroup>
            {checked &&
                <>
                    <Label>Areas</Label>
                    <Typeahead
                        labelKey="label"
                        id={name}
                        options={AreaOptions}
                        selected={Data?.areas || []}
                        onChange={(selected) => {
                            const updatedData = {
                                ...Data,
                                areas: selected,
                            };
                            setFormData((prev) => ({
                                ...prev,
                                [name]: updatedData,
                            }));
                        }}
                        placeholder="Areas"
                        multiple={true}
                        disabled={FormData.role_id == 8 ? true : false}
                        className={errors && errors[`${name}_areas`] ? "is-invalid" : ""}
                    />
                    {errors && errors[`${name}_areas`] && 
                        <div className="invalid-feedback" style={{display: 'block'}}>{errors[`${name}_areas`]}</div>
                    }
                    
                    {FormData.role_id == 8 ?  
                    <FormGroup className='my-3'>
                        <Label>Sub Areas</Label>
                        <Typeahead
                            labelKey="sub_area_name"
                            id={name}
                            options={GenerateSub_AreaOptions(All_Factories, Data?.areas?.map(item => item.area_id), AllFactoryIds)}
                            selected={Data?.sub_areas || []}
                            onChange={(selected) => {
                                const updatedData = {
                                    ...Data,
                                    sub_areas: selected,
                                };
                                setFormData((prev) => ({
                                    ...prev,
                                    [name]: updatedData,
                                }));
                            }}
                            placeholder="Sub Areas"
                            multiple={true}
                            className={errors && errors[`${name}_sub_areas`] ? "is-invalid" : ""}
                        />
                        {errors && errors[`${name}_sub_areas`] && 
                            <div className="invalid-feedback" style={{display: 'block'}}>{errors[`${name}_sub_areas`]}</div>
                        }
                    </FormGroup>
                    : <p className='text-muted text-center' style={{fontSize:'12px'}}>All sub areas for the selected area have been automatically selected.</p> }
                    
                    {flag ? (
                        <>
                            <Label>Phone Numbers</Label>
                            <Typeahead
                                id="phone_numbers"
                                labelKey="label"
                                multiple
                                options={[]}
                                selected={(FormData.whatsapp_notifications?.phone_numbers || []).map(email =>
                                    typeof email === 'string' ? email : email.label
                                )}
                                onChange={(selected) => {
                                    const emailLabels = selected.map(item => (typeof item === 'string' ? item : item.label));
                                    setFormData(prev => ({
                                        ...prev,
                                        whatsapp_notifications: {
                                            ...prev.whatsapp_notifications,
                                            phone_numbers: emailLabels,
                                        },
                                    }));
                                }}
                                placeholder="Enter phone number"
                                allowNew
                                newSelectionPrefix="Add Phone Number: "
                                disabled={!FormData.whatsapp_notifications?.toggle}
                                className={errors && errors.whatsapp_phone_numbers ? "is-invalid" : ""}
                            />
                            {errors && errors.whatsapp_phone_numbers && 
                                <div className="invalid-feedback" style={{display: 'block'}}>{errors.whatsapp_phone_numbers}</div>
                            }
                        </>
                    ) : (
                        <>
                            <Label>Email Addresses</Label>
                            <Typeahead
                                id="email_alert_emails"
                                labelKey="label"
                                multiple
                                options={[]}
                                selected={(FormData.email_notifications?.alert_emails || []).map(email =>
                                    typeof email === 'string' ? email : email.label
                                )}
                                onChange={(selected) => {
                                    const emailLabels = selected.map(item => (typeof item === 'string' ? item : item.label));
                                    setFormData(prev => ({
                                        ...prev,
                                        email_notifications: {
                                            ...prev.email_notifications,
                                            alert_emails: emailLabels,
                                        },
                                    }));
                                }}
                                placeholder="Enter email addresses"
                                allowNew
                                newSelectionPrefix="Add email: "
                                disabled={!FormData.email_notifications?.toggle}
                                className={errors && errors.email_alert_emails ? "is-invalid" : ""}
                            />
                            {errors && errors.email_alert_emails && 
                                <div className="invalid-feedback" style={{display: 'block'}}>{errors.email_alert_emails}</div>
                            }
                        </>
                    )}
                </>}
        </div>
    )
}
