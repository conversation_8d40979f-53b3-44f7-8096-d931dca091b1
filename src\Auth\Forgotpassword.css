 body {
     margin: 0;
     padding: 0;
     font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
 }

 .reset-password-container {
     display: flex;
     align-items: center;
     justify-content: center;
     min-height: 100vh;
     background: linear-gradient(135deg, #e39b7c 0%, #e56e3b 100%);
     position: relative;
     overflow: hidden;
     padding: 20px;
 }

 /* Floating shapes animation */
 .floating-shapes {
     position: absolute;
     width: 100%;
     height: 100%;
     top: 0;
     left: 0;
     z-index: 0;
 }

 .shape {
     position: absolute;
     background: rgba(255, 255, 255, 0.1);
     border-radius: 50%;
     animation: float 15s infinite ease-in-out;
 }

 .shape-1 {
     width: 120px;
     height: 120px;
     top: 20%;
     left: 10%;
     animation-delay: 3s;
 }

 .shape-2 {
     width: 80px;
     height: 80px;
     top: 60%;
     left: 20%;
     animation-delay: 2s;
 }

 .shape-3 {
     width: 100px;
     height: 100px;
     top: 30%;
     right: 15%;
     animation-delay: 4s;
 }

 .shape-4 {
     width: 60px;
     height: 60px;
     bottom: 20%;
     right: 25%;
     animation-delay: 6s;
 }

 @keyframes float {
     0% {
         transform: translateY(0) rotate(0deg);
         opacity: 0.8;
     }

     50% {
         transform: translateY(-20px) rotate(180deg);
         opacity: 0.5;
     }

     100% {
         transform: translateY(0) rotate(360deg);
         opacity: 0.8;
     }
 }

 /* Card styling */
 .reset-card {
     width: 100%;
     max-width: 450px;
     border-radius: 16px;
     box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
     background: #FFFFFF;
     position: relative;
     z-index: 1;
     overflow: hidden;
     transform: translateY(30px);
     opacity: 0;
     transition: all 0.6s ease;
 }

 .reset-card.show {
     transform: translateY(0);
     opacity: 1;
 }

 .reset-card::before {
     content: '';
     position: absolute;
     top: 0;
     left: 0;
     width: 100%;
     height: 6px;
    background: linear-gradient(135deg, #e39b7c 0%, #e56e3b 100%);
 }

 .card-title {
     color: #635470;
     font-weight: 700;
     margin-top: 15px;
     font-size: 28px;
 }

 .card-subtitle {
     color: #8a8a8a;
     font-size: 16px;
     margin-top: 10px;
 }

 /* Logo styling */
 .logo-container {
     display: flex;
     justify-content: center;
     margin-top: 20px;
 }

 .logo-circle {
     width: 80px;
     height: 80px;
     border-radius: 50%;
     background: linear-gradient(135deg, #e39b7c 0%, #e56e3b 100%);
     display: flex;
     align-items: center;
     justify-content: center;
     box-shadow: 0 5px 15px rgba(99, 84, 112, 0.4);
 }

 .lock-icon {
     display: inline-block;
     width: 30px;
     height: 30px;
     background-color: white;
     mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' strokeWidth='2' strokeLinecap='round' strokeLinejoin='round'%3E%3Crect x='3' y='11' width='18' height='11' rx='2' ry='2'%3E%3C/rect%3E%3Cpath d='M7 11V7a5 5 0 0 1 10 0v4'%3E%3C/path%3E%3C/svg%3E") no-repeat center center;
     -webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' strokeWidth='2' strokeLinecap='round' strokeLinejoin='round'%3E%3Crect x='3' y='11' width='18' height='11' rx='2' ry='2'%3E%3C/rect%3E%3Cpath d='M7 11V7a5 5 0 0 1 10 0v4'%3E%3C/path%3E%3C/svg%3E") no-repeat center center;
 }

 /* Form styling */
 .form-label {
     color: #e56e3b;
     font-weight: 600;
     font-size: 16px;
     margin-bottom: 8px;
 }

 .input-container {
     position: relative;
     transition: all 0.3s ease;
 }

 .input-container.focused {
     transform: scale(1.02);
 }

 .input-container.hovered {
     transform: translateY(-2px);
 }

 .email-icon {
     position: absolute;
     left: 15px;
     top: 50%;
     transform: translateY(-50%);
     display: inline-block;
     width: 20px;
     height: 20px;
     background-color: #1a3d6f;
     mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' strokeWidth='2' strokeLinecap='round' strokeLinejoin='round'%3E%3Cpath d='M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z'%3E%3C/path%3E%3Cpolyline points='22,6 12,13 2,6'%3E%3C/polyline%3E%3C/svg%3E") no-repeat center center;
     -webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' strokeWidth='2' strokeLinecap='round' strokeLinejoin='round'%3E%3Cpath d='M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z'%3E%3C/path%3E%3Cpolyline points='22,6 12,13 2,6'%3E%3C/polyline%3E%3C/svg%3E") no-repeat center center;
     z-index: 2;
 }

 .custom-input {
     height: 55px;
     border-radius: 10px;
     border: 2px solid #e0e0e0;
     padding-left: 45px;
     font-size: 16px;
     transition: all 0.3s ease;
     background-color: #f8f9fa;
 }

 .custom-input:focus {
     border-color: #e56e3b;
     box-shadow: 0 0 0 0.2rem rgba(99, 84, 112, 0.25);
     background-color: #fff;
 }

 /* Button styling */
 .submit-button {
     height: 55px;
     border-radius: 10px;
     font-size: 16px;
     font-weight: 600;
     letter-spacing: 0.5px;
     margin-top: 20px;
     background: linear-gradient(135deg, #e39b7c 0%, #e56e3b 100%);
     border: none;
     box-shadow: 0 5px 15px rgba(99, 84, 112, 0.4);
     transition: all 0.3s ease;
 }

 .submit-button:hover:not(:disabled) {
     transform: translateY(-3px);
     box-shadow: 0 8px 20px #635470;
     background: linear-gradient(135deg, #e39b7c 0%, #e56e3b 100%);
 }

 .submit-button:active:not(:disabled) {
     transform: translateY(0);
     box-shadow: 0 3px 10px #e56e3b;
 }

 .submit-button:disabled {
     background: #e39b7c;
     cursor: not-allowed;
 }

 /* Error message styling */
 .error-message {
     border-radius: 8px;
     display: flex;
     align-items: center;
     padding: 12px 15px;
     font-size: 14px;
     background-color: rgba(220, 53, 69, 0.1);
     border: none;
     color: #dc3545;
 }

 .error-icon {
     display: inline-block;
     width: 16px;
     height: 16px;
     margin-right: 8px;
     background-color: #dc3545;
     mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' strokeWidth='2' strokeLinecap='round' strokeLinejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E") no-repeat center center;
     -webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' strokeWidth='2' strokeLinecap='round' strokeLinejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E") no-repeat center center;
 }

 /* Success message styling */
 .success-container {
     padding: 20px 0;
 }

 .success-icon {
     width: 80px;
     height: 80px;
     border-radius: 50%;
     background: linear-gradient(135deg, #28a745, #20c997);
     display: inline-flex;
     align-items: center;
     justify-content: center;
     box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
     transition: all 0.3s ease;
 }

 .success-icon.pulse {
     animation: pulse 0.5s ease-in-out;
 }

 @keyframes pulse {
     0% {
         transform: scale(0.5);
         opacity: 0;
     }

     50% {
         transform: scale(1.2);
     }

     100% {
         transform: scale(1);
         opacity: 1;
     }
 }

 .check-icon {
     display: inline-block;
     width: 30px;
     height: 30px;
     background-color: white;
     mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' strokeWidth='2' strokeLinecap='round' strokeLinejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E") no-repeat center center;
     -webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' strokeWidth='2' strokeLinecap='round' strokeLinejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E") no-repeat center center;
 }

 .success-message {
     color: #6c757d;
     margin-top: 15px;
     font-size: 16px;
 }

 /* Footer styling */
 .footer-links {
     margin-top: 10px;
 }

 .back-button {
     color: #e39b7c;
     font-weight: 500;
     text-decoration: none;
 }

 .back-button:hover {
     text-decoration: underline;
 }

 /* Animation for error */
 @keyframes shake {

     0%,
     100% {
         transform: translateX(0);
     }

     10%,
     30%,
     50%,
     70%,
     90% {
         transform: translateX(-5px);
     }

     20%,
     40%,
     60%,
     80% {
         transform: translateX(5px);
     }
 }

 .shake {
     animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
 }