import React, { useState } from 'react'
import { Card, CardBody, CardHeader, Col, Row } from 'reactstrap';
import '../style/style.css'
import Loader3 from '../../../../../../CommonElements/Spinner/loader3'
import { FaArrowTrendUp } from "react-icons/fa6";
import { FaArrowTrendDown } from "react-icons/fa6";
import { IoWarning } from "react-icons/io5";
import { PiWarningOctagon } from "react-icons/pi";
import { useNavigate } from 'react-router';
import { areas } from '../../../../../../Data/staticData/data';

const HighestOrHighSeverity = ({ data, filters, heading, highestboolean, area, subarea, owner, module, alert, loader, shift, HighestNonComplianceArea, highestAlertModule, highestAlertOwner, MaxModuleAlerts, maxValue, AreaDashboard }) => {
const role = JSON.parse(localStorage.getItem('role'))
  // function findSeverity(type) {
  //   const lowerType = type.toLowerCase()
  //   console.log('lowerTypee', lowerType)
  //   if (lowerType === 'mmhe' || 'emergency exit') {
  //     console.log('bilal1')
  //     return 'High'
  //   } else if (lowerType === 'helmet' || lowerType === 'vest') {
  //     console.log('bilal2')
  //     return 'Medium plus'
  //   } else if (lowerType === 'machine guard') {
  //     console.log('bilal3')
  //     return 'low'
  //   } else {
  //     return 'Medium'
  //   }

  // }
  function findSeverity(type) {
    const lowerType = type.toLowerCase();
    console.log('lowerType', lowerType);

    if (lowerType === 'mmhe' || lowerType === 'emergency exit') {
      console.log('bilal1');
      return 'High';
    } else if (lowerType === 'helmet' || lowerType === 'vest') {
      console.log('bilal2');
      return 'Medium plus';
    } else if (lowerType === 'machine guard') {
      console.log('bilal3');
      return 'low';
    } else {
      return 'Medium';
    }
  }
  const Navigate = useNavigate()
  const handleNavigate = () => {
    const navigateData = {
      area: AreaDashboard ? "" : maxValue?.violationArea ? maxValue?.violationArea : '',
      // area: "",
      subarea: !AreaDashboard ? "" : maxValue?.violationArea ? maxValue?.violationArea : '',
      // shift: [`Shift ${shift}`] || [filters?.shift],
      // subarea:"",
      shift: filters?.shift ? [filters?.shift] : [],
      // shift:[],
      severity: '',
      module: maxValue?.violationType ? maxValue?.violationType : '',
    }
    console.log('navigateDataa', navigateData)
    const liveFilters = {
      ...navigateData,
      approval: "Select Approval",
      date: "",
      week: filters?.weekly,
      month: filters?.month,
      starting: "",
      ending: ""
    }
    localStorage.setItem('high_severity_alerts', JSON.stringify(liveFilters));
    const url = `${process.env.PUBLIC_URL}/default/high_severity_alerts/${JSON.parse(localStorage.getItem('role'))}`;
    Navigate(url);
  }


  console.log('maxValuemaxValue', maxValue)

  return (
    <Card onClick={() => handleNavigate()} style={{ borderRadius: '24px', flex: '1', minHeight: '170px', maxHeight: 'auto', cursor: 'pointer' }}  >
      {
      loader ? <span className="w-100 h-100 d-flex justify-content-center align-items-center position-absolute"><Loader3 /></span> :
        <CardBody className='p-4'>
            <p className='ellipsis-text mb-0' style={{ fontSize: '16px', color: '#383838', fontWeight: '400' }}>{heading}</p>
            <div className=' d-flex gap-2 justify-content-between'>
              <div className='d-flex align-items-center gap-2'>
                {highestboolean ?
                  <IoWarning style={{ width: '40px', height: '40px', borderRadius: '50%', padding: '8px', background: '#175FA4', color: 'white' }} />
                  :
                  < PiWarningOctagon style={{ width: '40px', height: '40px', borderRadius: '20px', padding: '5px', background: '#175FA4', color: 'white' }} />
                }
                <p className='ellipsis-text m-0' style={{ color: '#595959', fontSize: '16px', fontSize: '30px', fontWeight: '500' }} >{role === 'area' ? maxValue?.main : maxValue?.highSeverityTotalAlerts || maxValue?.mediumPlusSeverityAlerts || 'N/A'}</p>
              </div>
              <div className='overflow-hidden'>
                <p className='m-0 p-0 ellipsis-text ' style={{ color: '#8C8C8C', fontSize: '12px' }}>Details</p>
                {/* <p className='m-0 p-0 ellipsis-text' style={{ fontSize: '12px', width: '100%' }}>Area: {area || 'N/A'}</p> */}
                <p className='m-0 p-0 ellipsis-text' style={{ fontSize: '12px' }}>Violation Type: {maxValue?.violationType || 'N/A'}</p>
                {/* <p className='m-0 p-0 ellipsis-text' style={{ fontSize: '12px' }}>Alerts: {MaxModuleAlerts || 'N/A'}/{HighestNonComplianceArea || 'N/A'}</p> */}
                <p className='m-0 p-0 ellipsis-text' style={{ fontSize: '12px' }}>Alerts: {maxValue?.alertLeft || 'N/A'}/{role === 'area' ? maxValue?.main : maxValue?.highSeverityTotalAlerts || maxValue?.mediumPlusSeverityAlerts|| 'N/A'}</p>

                {/* <p className='m-0 p-0 ellipsis-text' style={{ fontSize: '12px' }}>Sub Area: {subarea || 'N/A'}</p> */}
                <p className='m-0 p-0 ellipsis-text' style={{ fontSize: '12px', }}>Higest violation Area: { role === 'area' ? maxValue?.violationArea : maxValue?.violation_areaOwner || 'N/A'} </p>
                {/* {filters?.shift && <p className='m-0 p-0 ellipsis-text' style={{ fontSize: '12px' }}>Shift: {shift || 'N/A'} </p>} */}
              </div>
            </div>





        </CardBody>
      }
    </Card>
  )
}

export default HighestOrHighSeverity;