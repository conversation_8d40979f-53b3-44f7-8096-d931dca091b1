import { errorToast } from "../_helper/helper";

export const formateDate = (dateString) => {
    if (!dateString) return ''; 

    // Split the dateString into parts
    const dateParts = dateString.split('-');
    const year = dateParts[0];
    const monthIndex = parseInt(dateParts[1], 10) - 1; // Convert month to a 0-based index for JavaScript's Date object
    console.log('month Inde', monthIndex)
    const day = dateParts[2].padStart(2, '0'); 
  
    // Get the month name using the month index
    const month = new Date(0, monthIndex).toLocaleString('default', { month: 'long' });
  
    return `${day} ${month}, ${year}`;
  
  };


  // export const formatMonth = (monthValue) => {
  //   const date = new Date(`${monthValue}-01`);
  //   const monthName = date.toLocaleString('default', { month: 'long' });
  //   const year = date.getFullYear();
  //   return `${monthName} ${year}`;
  // };
  export const formatMonth = (monthValue) => {
    const [year, month] = monthValue.split('-').map(Number);
    const date = new Date(Date.UTC(year, month - 1, 1)); // Force UTC
    const monthName = date.toLocaleString('default', { month: 'long', timeZone: 'UTC' });
    return `${monthName} ${year}`;
  };
  

  export const formatWeek = (weekValue) => {
    const [year, week] = weekValue.split('-W');
    return `${year} - Week ${week}`;
  };

  export const formatMonth2 = (monthString) => {
    const date = new Date(monthString + "-01");
    return date.toLocaleString('default', { month: 'long', year: 'numeric' });
  };

  export function isArrayValid(arr) {
    return arr.every((obj, index) => {
        console.log(`Checking object at index ${index}:`, obj);
  
        return Object.values(obj).every((value, key) => {
            const isValid = value !== null && value !== "" && !(Array.isArray(value) && value.length === 0);
  
            if (!isValid) {
                console.warn(`Invalid value found at index ${index}, key ${key}:`, value);
            }
  
            return isValid;
        });
    });
  }
  
  
  export function transformData(data,details) {   
    if(!details){
      errorToast("Error while transforming data")
      return 
    }
    return {
        camera_id: details.camera_id, 
        roi_data: data.map(item => ({
            module_id: item.id, 
            model: item.model, 
            roi: item.roi.map(region => 
                region.coordinates.map(coord => ({ x: coord.x, y: coord.y }))
            ),
            alert_frequency: item.alert,
            color: item.color,
            end_time: item.end_time,
            frequency_time: item.freq,
            level: item.level, 
            no_of_person: item.people,
            start_time: item.start_time,
            // camera_brand:item.camera_brand
            
        }))
    };
  }

export const convertToLocalTimezone = (dateTimeString) => {
  if (!dateTimeString) return '';
  
  try {
    // Parse the input date string
    // If the string doesn't specify a timezone, assume it's UTC
    const date = dateTimeString.includes('Z') || dateTimeString.includes('+') 
      ? new Date(dateTimeString) 
      : new Date(dateTimeString + 'Z'); // Append Z to treat as UTC if no timezone info
    
    // Check if date is valid
    if (isNaN(date.getTime())) {
      console.error('Invalid date format:', dateTimeString);
      return '';
    }
    
    // Get user's timezone
    const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    
    // Format the date to user's local timezone with 24-hour format
    const options = {
      timeZone: userTimezone,
      year: 'numeric',
      month: 'numeric',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false // 24-hour format
    };
    
    const formattedDate = new Intl.DateTimeFormat('en-US', options).format(date);
    console.log(`Original UTC: ${dateTimeString} → Local: ${formattedDate} (${userTimezone})`);
    
    return formattedDate;
  } catch (error) {
    console.error('Error converting time to local timezone:', error);
    return dateTimeString; // Return original string if conversion fails
  }
};

export function generateTableData({ type, modules, selectedmodules, NormaliseRoisData, userCurrentSelectedRoi }) {
  if (!modules?.length || !selectedmodules?.length) return [];

  if (type === "add") {
    return modules
      .filter(module => selectedmodules.includes(module.module_id))
      .map((module) => ({
        roi: [],
        model: module.module_name,
        start_time: "",
        end_time: "",
        people: "",
        freq: "",
        level: "",
        color: "",
        id: module.module_id,
        alert: '',
        // camera_brand: ''
      }));
  }

  if (type === "edit" && NormaliseRoisData?.moduleWiseSelections?.length > 0) {
    // Create a set of allowed ROI names
    const allowedRoiNames = new Set(userCurrentSelectedRoi?.map(roi => roi.name));

    return NormaliseRoisData.moduleWiseSelections
      .filter(module => selectedmodules.includes(module.module_id))
      .map((module) => {
        const roiData = module.selections
          .filter(selectionName => allowedRoiNames.has(selectionName)) // Only keep allowed ROIs
          .map(selectionName => {
            const roi = NormaliseRoisData.normalizedRois.find(r => r.name === selectionName);
            if (roi) {
              return {
                ...roi,
                label: roi.name,
                value: roi.id,
                name: roi.name
              };
            }
            return null;
          })
          .filter(Boolean);

        return {
          roi: roiData,
          model: modules.find(e => e.module_id === module.module_id)?.module_name || "",
          start_time: module.start_time,
          end_time: module.end_time,
          people: module.no_of_person,
          freq: module.frequency_time,
          level: module.level,
          color: module.color,
          id: module.module_id,
          alert: module.alert_frequency,
          // camera_brand: ''
        };
      });
  }

  return [];
}

export function getNextRoiName(rois) {
  if (!Array.isArray(rois)) return 1;

  // Extract numbers from names like "R1", "R4"
  const numbers = rois
    .map(roi => {
      const match = roi.name?.match(/^R(\d+)$/);
      return match ? parseInt(match[1], 10) : null;
    })
    .filter(num => num !== null);

  const max = numbers.length > 0 ? Math.max(...numbers) : 0;

  return max + 1;
}

export function normalizeRoisWithModules(data) {
  const uniqueRois = [];
  const roiMap = []; // Final list of unique ROIs
  const moduleSelectionMap = []; // Module-wise selections
  let roiId = 192;
  let roiCount = 1;

  const isSameRoi = (roi1, roi2) => {
    if (roi1.length !== roi2.length) return false;
    return roi1.every((point, index) => {
      const other = roi2[index];
      return Math.abs(point.x - other.x) < 1e-6 && Math.abs(point.y - other.y) < 1e-6;
    });
  };

  data.forEach(entry => {
    const moduleSelections = [];

    entry.roi.forEach(polygon => {
      let matchIndex = uniqueRois.findIndex(existing => isSameRoi(existing, polygon));

      if (matchIndex === -1) {
        uniqueRois.push(polygon);
        matchIndex = uniqueRois.length - 1;

        roiMap.push({
          coordinates: polygon,
          name: `R${roiCount}`,
          id: roiId
        });

        roiId++;
        roiCount++;
      }

      moduleSelections.push(`R${matchIndex + 1}`);
    });

    // Add full module data into moduleSelectionMap
    moduleSelectionMap.push({
      module_id: entry.module_id,
      selections: moduleSelections,
      start_time: entry.start_time || "",
      end_time: entry.end_time || "",
      frequency_time: entry.frequency_time || "",
      no_of_person: entry.no_of_person || "",
      level: entry.level || "",
      color: entry.color || "",
      alert_frequency: entry.alert_frequency || "",
      // camera_brand: entry.camera_brand || ""
    });
  });

  return {
    normalizedRois: roiMap,
    moduleWiseSelections: moduleSelectionMap
  };
}


export function validateFilters(filters, showAlert = true) {
  const today = new Date();
  if(filters.shifts.length > 1){
   if (showAlert == true) {errorToast("Please select one shift to generate report")}
    return false
  }
  // helpers
  const pad = (n) => String(n).padStart(2, "0");
  const currentDate = `${today.getFullYear()}-${pad(today.getMonth() + 1)}-${pad(today.getDate())}`;
  const currentMonth = `${today.getFullYear()}-${pad(today.getMonth() + 1)}`;

  // ISO week (YYYY-Www)
  const getISOWeek = (date) => {
    const tmp = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = tmp.getUTCDay() || 7;
    tmp.setUTCDate(tmp.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(tmp.getUTCFullYear(), 0, 1));
    const weekNum = Math.ceil((((tmp - yearStart) / 86400000) + 1) / 7);
    return `${tmp.getUTCFullYear()}-W${pad(weekNum)}`;
  };
  const currentWeek = getISOWeek(today);

  // ----- Week / Month guards -----
  if (filters.week && filters.week === currentWeek) {
    if (showAlert) errorToast("Selected week is not completed yet. Please select the previous week to generate the report.");
    return false;
  }

  if (filters.month && filters.month === currentMonth) {
    if (showAlert) errorToast("Selected month is not completed yet. Please select the previous month to generate the report.");
    return false;
  }

  // ----- Shift helpers -----
  const normalizeShifts = (arr) =>
    Array.isArray(arr)
      ? arr
          .map((s) => String(s).trim().toUpperCase().replace(/\s+/g, " "))
          .map((s) => {
            if (s === "SHIFT A" || s === "A") return "A";
            if (s === "SHIFT B" || s === "B") return "B";
            if (s === "SHIFT C" || s === "C") return "C";
            return null;
          })
          .filter(Boolean)
      : [];

  const makeDateTime = (yyyyMmDd, hh, mm = 0) => {
    const [y, m, d] = yyyyMmDd.split("-").map(Number);
    return new Date(y, m - 1, d, hh, mm, 0, 0);
  };

  const getShiftWindow = (yyyyMmDd, shift) => {
    if (shift === "A") {
      return { start: makeDateTime(yyyyMmDd, 8), end: makeDateTime(yyyyMmDd, 16) };
    }
    if (shift === "B") {
      return { start: makeDateTime(yyyyMmDd, 16), end: makeDateTime(yyyyMmDd, 24) };
    }
    // C
    return { start: makeDateTime(yyyyMmDd, 0), end: makeDateTime(yyyyMmDd, 8) };
  };

  const areSelectedShiftsCompleted = (yyyyMmDd, shiftsArr) => {
    const now = new Date();
    const normalized = normalizeShifts(shiftsArr);
    if (normalized.length === 0) {
      return { ok: false, pending: [], noneSelected: true };
    }
    const pending = [];
    for (const s of normalized) {
      const { end } = getShiftWindow(yyyyMmDd, s);
      if (now < end) pending.push(s);
    }
    return { ok: pending.length === 0, pending, noneSelected: false };
  };

  // ----- Current date check -----
  if (filters.date && filters.date === currentDate) {
    const { ok, pending, noneSelected } = areSelectedShiftsCompleted(filters.date, filters.shifts);
    if (noneSelected) {
      if (showAlert) errorToast("For the current day, please select at least one shift (A: 08-16, B: 16-24, C: 00-08) to generate the report.");
      return false;
    }
    if (!ok) {
      if (showAlert) {
        if (pending.length === 1) {
          errorToast(`The selected shift (${pending[0]}) for today is not yet completed. Please wait until it finishes or select the previous day.`);
        } else {
          errorToast(`The following selected shifts for today are not yet completed: ${pending.join(", ")}. Please wait until they finish or select the previous day.`);
        }
      }
      return false;
    }
    return true;
  }

  // ----- Ending date check (if equals today) -----
  if (filters.ending && filters.ending === currentDate) {
    const { ok, pending, noneSelected } = areSelectedShiftsCompleted(filters.ending, filters.shifts);
    if (noneSelected) {
      if (showAlert) errorToast("Ending date is today. Please select at least one shift (A: 08-16, B: 16-24, C: 00-08) to generate the report.");
      return false;
    }
    if (!ok) {
      if (showAlert) {
        if (pending.length === 1) {
          errorToast(`The selected ending shift (${pending[0]}) is not yet completed. Please wait until it finishes or select the previous day.`);
        } else {
          errorToast(`The following selected ending shifts are not yet completed: ${pending.join(", ")}. Please wait until they finish or select the previous day.`);
        }
      }
      return false;
    }
    return true;
  }

  return true;
}



