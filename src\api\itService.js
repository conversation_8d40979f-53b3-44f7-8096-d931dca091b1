import api from './api'

const itService = {
    activityLogs (payload) {
        return api.put(`users/get_activity_log`, payload)
    },
    getLogs () {
        return api.get(`users/get_user_logs`)
    },
    getallareas(){
        return api.get('/factory_area/get_all_areas')
    },
    addArea(payload) {
        return api.post(`/factory_area/add_area`, payload, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
    },
    SubArea(payload) {
        return api.post(`/sub_area/add_subarea`, payload, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
    },
    get_all_subarea(){
        return api.get('/sub_area/get_all_sub_areas')
    },
    updateArea(payload) {
        return api.put('/factory_area/update_area', payload,{
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
    },
    update_subarea_status(id){
        return api.put(`/sub_area/update_sub_area_status/${id}`);
    },
    update_subarea_details(payload){
        return api.put(`/sub_area/update_sub_area`, payload);
    },
    get_all_release_notes(){
        return api.get('/release_notes/')
    },
    add_release_notes(payload){
        return api.post('/release_notes/', payload)
    },
    update_release_notes(id,payload){
        return api.put(`/release_notes/${id}`, payload)
    },
    delete_release_notes(id){
        return api.delete(`/release_notes/${id}`);
    },

   
}

export default itService;